// components/ui/AuthStatus.tsx
import { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Key } from 'lucide-react';

export function AuthStatus() {
  const [token, setToken] = useState<string | null>(null);
  const [isValid, setIsValid] = useState<boolean | null>(null);

  useEffect(() => {
    // Check for token in localStorage
    const customToken = localStorage.getItem('customToken');
    setToken(customToken);
    
    // Basic token validation (check if it exists and looks like a JWT)
    if (customToken) {
      try {
        // Basic JWT format check (has 3 parts separated by dots)
        const parts = customToken.split('.');
        setIsValid(parts.length === 3);
      } catch {
        setIsValid(false);
      }
    } else {
      setIsValid(false);
    }
  }, []);

  const getStatusColor = () => {
    if (isValid === null) return 'text-gray-500';
    return isValid ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = () => {
    if (isValid === null) return <Key className="w-4 h-4" />;
    return isValid ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (isValid === null) return 'Checking...';
    if (isValid) return 'Token Valid';
    return token ? 'Invalid Token' : 'No Token Found';
  };

  const handleRefreshToken = () => {
    window.location.reload();
  };

  return (
    <div className="flex items-center gap-2 text-sm">
      <div className={`flex items-center gap-1 ${getStatusColor()}`}>
        {getStatusIcon()}
        <span>{getStatusText()}</span>
      </div>
      
      {!isValid && (
        <button
          onClick={handleRefreshToken}
          className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
        >
          Refresh
        </button>
      )}
      
      {/* Debug info - only show in development */}
      {process.env.NODE_ENV === 'development' && token && (
        <details className="text-xs text-gray-500">
          <summary className="cursor-pointer">Debug</summary>
          <div className="mt-1 p-2 bg-gray-100 rounded text-xs font-mono break-all max-w-xs">
            Token: {token.substring(0, 20)}...
          </div>
        </details>
      )}
    </div>
  );
}
