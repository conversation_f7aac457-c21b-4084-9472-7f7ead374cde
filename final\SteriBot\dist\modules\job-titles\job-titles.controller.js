"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobTitlesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const job_titles_service_1 = require("./job-titles.service");
const create_job_title_dto_1 = require("./dto/create-job-title.dto");
const update_job_title_dto_1 = require("./dto/update-job-title.dto");
const firebase_auth_guard_1 = require("../../common/guards/firebase-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let JobTitlesController = class JobTitlesController {
    constructor(jobTitlesService) {
        this.jobTitlesService = jobTitlesService;
    }
    create(createJobTitleDto) {
        return this.jobTitlesService.create(createJobTitleDto);
    }
    findAll() {
        return this.jobTitlesService.findAll();
    }
    findOne(id) {
        return this.jobTitlesService.findById(id);
    }
    findByDepartment(departmentId) {
        return this.jobTitlesService.findByDepartment(departmentId);
    }
    update(id, updateJobTitleDto) {
        return this.jobTitlesService.update(id, updateJobTitleDto);
    }
    remove(id) {
        return this.jobTitlesService.remove(id);
    }
};
exports.JobTitlesController = JobTitlesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Create a new job title (Admin only)" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_job_title_dto_1.CreateJobTitleDto]),
    __metadata("design:returntype", void 0)
], JobTitlesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get all job titles" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], JobTitlesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job title by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], JobTitlesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('department/:departmentId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job titles by department' }),
    __param(0, (0, common_1.Param)('departmentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], JobTitlesController.prototype, "findByDepartment", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Update job title (Admin only)" }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_job_title_dto_1.UpdateJobTitleDto]),
    __metadata("design:returntype", void 0)
], JobTitlesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete job title (Admin only)' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], JobTitlesController.prototype, "remove", null);
exports.JobTitlesController = JobTitlesController = __decorate([
    (0, swagger_1.ApiTags)("Job Titles"),
    (0, common_1.Controller)("job-titles"),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [job_titles_service_1.JobTitlesService])
], JobTitlesController);
//# sourceMappingURL=job-titles.controller.js.map