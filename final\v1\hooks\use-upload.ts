/**
 * Upload Hook
 */

import { useState, useCallback } from 'react';
import { uploadAPI, PhotoUploadResponse } from '@/api';

export interface UseUploadReturn {
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  uploadedUrl: string | null;
  uploadProfilePhoto: (file: File) => Promise<string | null>;
  uploadFile: (file: File, endpoint: string) => Promise<string | null>;
  clearError: () => void;
  reset: () => void;
}

export const useUpload = (): UseUploadReturn => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Reset all states
  const reset = useCallback(() => {
    setIsUploading(false);
    setUploadProgress(0);
    setError(null);
    setUploadedUrl(null);
  }, []);

  // Upload profile photo
  const uploadProfilePhoto = useCallback(async (file: File): Promise<string | null> => {
    try {
      setIsUploading(true);
      setError(null);
      setUploadProgress(0);
      
      // Validate file first
      const validation = uploadAPI.validateFile(file);
      if (!validation.isValid) {
        setError(validation.error || 'Invalid file');
        return null;
      }

      // Simulate progress (since we don't have real progress tracking)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await uploadAPI.uploadProfilePhoto(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      if (response.success && response.data?.photoUrl) {
        setUploadedUrl(response.data.photoUrl);
        return response.data.photoUrl;
      } else {
        setError(response.error || 'Failed to upload photo');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload photo';
      setError(errorMessage);
      return null;
    } finally {
      setIsUploading(false);
    }
  }, []);

  // Upload general file
  const uploadFile = useCallback(async (file: File, endpoint: string): Promise<string | null> => {
    try {
      setIsUploading(true);
      setError(null);
      setUploadProgress(0);
      
      // Validate file first
      const validation = uploadAPI.validateFile(file);
      if (!validation.isValid) {
        setError(validation.error || 'Invalid file');
        return null;
      }

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await uploadAPI.uploadFile(file, endpoint);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      if (response.success && response.data?.url) {
        setUploadedUrl(response.data.url);
        return response.data.url;
      } else {
        setError(response.error || 'Failed to upload file');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload file';
      setError(errorMessage);
      return null;
    } finally {
      setIsUploading(false);
    }
  }, []);

  return {
    isUploading,
    uploadProgress,
    error,
    uploadedUrl,
    uploadProfilePhoto,
    uploadFile,
    clearError,
    reset,
  };
};
