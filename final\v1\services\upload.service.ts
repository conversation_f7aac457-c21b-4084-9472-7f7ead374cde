/**
 * Upload Service - <PERSON>les file upload operations
 */

export interface PhotoUploadResponse {
  success: boolean;
  photoUrl?: string;
  picture?: string;
  url?: string;
  message?: string;
  error?: string;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

class UploadService {
  private readonly baseUrl: string;
  private readonly maxFileSize: number = 5 * 1024 * 1024; // 5MB

  constructor() {
    this.baseUrl = 'http://localhost:3001/api/v1';
  }

  private getAuthTokens() {
    return {
      customToken: localStorage.getItem('customToken'),
      authToken: localStorage.getItem('authToken'),
      userId: localStorage.getItem('userId'),
    };
  }

  private getAuthHeaders(): Record<string, string> {
    const { customToken, authToken } = this.getAuthTokens();
    const token = customToken || authToken;
    
    if (!token) {
      throw new Error('No authentication token found');
    }

    return {
      'Authorization': `<PERSON><PERSON> ${token}`,
      // Note: Don't set Content-Type for FormData, let browser set it with boundary
    };
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): FileValidationResult {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      return {
        isValid: false,
        error: 'Please select an image file',
      };
    }

    // Validate file size
    if (file.size > this.maxFileSize) {
      return {
        isValid: false,
        error: 'File size must be less than 5MB',
      };
    }

    return { isValid: true };
  }

  /**
   * Upload user profile photo
   */
  async uploadProfilePhoto(file: File): Promise<string> {
    try {
      // Validate file first
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      const { userId } = this.getAuthTokens();
      if (!userId) {
        throw new Error('User ID not found');
      }

      console.log('🔍 Upload Debug Info:');
      console.log('User ID:', userId);
      console.log('File name:', file.name);
      console.log('File size:', file.size);
      console.log('File type:', file.type);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('photo', file);

      // Log FormData contents
      console.log('📤 FormData contents:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const response = await fetch(`${this.baseUrl}/users/${userId}/photo`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: formData,
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

      // Try to get response text for debugging
      const responseText = await response.text();
      console.log('📥 Response body:', responseText);

      if (!response.ok) {
        // Try to parse error message from response
        let errorMessage = `Failed to upload photo: ${response.status}`;
        try {
          const errorData = JSON.parse(responseText);
          if (errorData.message || errorData.error) {
            errorMessage = errorData.message || errorData.error;
          }
        } catch (e) {
          // If response is not JSON, use the text as error message
          if (responseText) {
            errorMessage = responseText;
          }
        }
        throw new Error(errorMessage);
      }

      // Parse the successful response
      const result = JSON.parse(responseText);
      console.log('✅ Upload successful:', result);

      // Extract photo URL from response
      const newPhotoUrl = result.photoUrl || result.picture || result.url;
      if (!newPhotoUrl) {
        console.warn('⚠️ Upload successful but no photo URL in response:', result);
        throw new Error('Upload successful but no photo URL received');
      }

      console.log('✅ Photo uploaded successfully:', newPhotoUrl);
      return newPhotoUrl;
    } catch (error) {
      console.error('❌ Error uploading photo:', error);
      throw error instanceof Error ? error : new Error('Failed to upload photo');
    }
  }
}

// Export singleton instance
export const uploadService = new UploadService();
