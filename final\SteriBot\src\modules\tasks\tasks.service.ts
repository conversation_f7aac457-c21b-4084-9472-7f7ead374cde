import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateTaskDto } from "./dto/create-task.dto"
import type { UpdateTaskDto } from "./dto/update-task.dto"

@Injectable()
export class TasksService {
  private readonly collection = "tasks"

  constructor(private firebaseService: FirebaseService) {}

  async create(createTaskDto: CreateTaskDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const taskData = {
      ...createTaskDto,
      taskId: docRef.id,
      status: "pending",
      createdAt: new Date(),
    }

    await docRef.set(taskData)
    return taskData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Task with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async findByUser(userId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where("createdBy", "==", userId).get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findByRobot(robotId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where("robotId", "==", robotId).get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async update(id: string, updateTaskDto: UpdateTaskDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateTaskDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async updateStatus(id: string, status: string) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    const updateData: any = {
      status,
      updatedAt: new Date(),
    }

    if (status === "completed") {
      updateData.completedAt = new Date()
    }

    await docRef.update(updateData)
    return this.findById(id)
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "Task deleted successfully" }
  }
}
