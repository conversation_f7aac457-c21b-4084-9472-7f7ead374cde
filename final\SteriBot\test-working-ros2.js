#!/usr/bin/env node
/**
 * Test script for working ROS2 bridge
 * Tests the key functionality now that ROS2 is working
 */

const http = require('http');
const WebSocket = require('ws');

const BACKEND_HOST = 'localhost';
const BACKEND_PORT = 3001;

console.log('🤖 SteriBot ROS2 Integration Test');
console.log('🎯 Testing with WORKING ROS2 bridge at *************:9090');
console.log('=' .repeat(60));

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: BACKEND_HOST,
      port: BACKEND_PORT,
      path: path,
      method: method,
      headers: { 'Content-Type': 'application/json' }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => { responseData += chunk; });
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', reject);
    if (data) req.write(JSON.stringify(data));
    req.end();
  });
}

async function testROS2Connection() {
  console.log('🔗 Step 1: Testing ROS2 connection...');
  try {
    const result = await makeRequest('/api/v1/robots/test/connection', 'POST');
    if (result.status === 201 && result.data.success) {
      console.log(`   ✅ ROS2 Status: ${result.data.connection_test.status}`);
      console.log(`   📍 Position: ${result.data.connection_test.has_position ? '✅ Available' : '❌ Not available'}`);
      console.log(`   🔋 Battery: ${result.data.connection_test.has_battery ? '✅ Available' : '❌ Not available'}`);
      console.log(`   ⏰ Last Updated: ${result.data.connection_test.last_updated}`);
      return result.data;
    } else {
      console.log(`   ❌ Connection failed: ${result.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return false;
  }
}

async function testRealtimeStream() {
  console.log('\n📡 Step 2: Testing real-time stream...');
  try {
    const streamConfig = {
      ip_address: '*************',
      port: 9090,
      include_robot_data: true,
      include_map_data: true,
      update_frequency: 1
    };

    const result = await makeRequest('/api/v1/robots/realtime/start', 'POST', streamConfig);
    if (result.status === 201 && result.data.success) {
      console.log(`   ✅ Stream started: ${result.data.session_id}`);
      console.log(`   🌐 WebSocket: ${result.data.websocket_info.url}`);
      return result.data.session_id;
    } else {
      console.log(`   ❌ Stream failed: ${result.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return false;
  }
}

async function testWebSocketData(duration = 10000) {
  console.log('\n🔌 Step 3: Testing WebSocket data reception...');
  
  return new Promise((resolve) => {
    try {
      const ws = new WebSocket(`ws://${BACKEND_HOST}:${BACKEND_PORT}/realtime-robot-data`);
      let connected = false;
      let dataReceived = { robot_data: 0, map_data: 0, connection: 0, other: 0 };
      
      const timeout = setTimeout(() => {
        ws.close();
        console.log(`   📊 Data received in ${duration/1000}s:`);
        console.log(`      🤖 Robot data: ${dataReceived.robot_data} messages`);
        console.log(`      🗺️  Map data: ${dataReceived.map_data} messages`);
        console.log(`      🔗 Connection: ${dataReceived.connection} messages`);
        console.log(`      📦 Other: ${dataReceived.other} messages`);
        
        const totalMessages = Object.values(dataReceived).reduce((a, b) => a + b, 0);
        resolve(totalMessages > 0);
      }, duration);

      ws.on('open', () => {
        connected = true;
        console.log('   ✅ WebSocket connected - listening for data...');
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          if (message.event) {
            dataReceived[message.event] = (dataReceived[message.event] || 0) + 1;
          } else {
            dataReceived.other++;
          }
          
          // Show first few messages
          const total = Object.values(dataReceived).reduce((a, b) => a + b, 0);
          if (total <= 5) {
            console.log(`   📨 ${message.event || 'unknown'}: ${JSON.stringify(message.data || {}).substring(0, 80)}...`);
          }
        } catch (e) {
          dataReceived.other++;
        }
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        console.log(`   ❌ WebSocket error: ${error.message}`);
        resolve(false);
      });

    } catch (error) {
      console.log(`   ❌ Setup error: ${error.message}`);
      resolve(false);
    }
  });
}

async function testMapSave() {
  console.log('\n🗺️  Step 4: Testing "Save Map" button...');
  try {
    const result = await makeRequest('/api/v1/robots/map/save', 'POST');
    if (result.status === 201 && result.data.success) {
      console.log(`   ✅ Map saved: ${result.data.filename}`);
      console.log(`   📁 Size: ${(result.data.file_size / 1024).toFixed(1)} KB`);
      console.log(`   🔗 Download: ${result.data.download_url}`);
      return result.data;
    } else {
      console.log(`   ❌ Save failed: ${result.status}`);
      console.log(`   Error: ${JSON.stringify(result.data)}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return false;
  }
}

async function cleanup(sessionId) {
  if (sessionId) {
    console.log('\n🧹 Cleanup: Stopping stream...');
    try {
      await makeRequest('/api/v1/robots/realtime/stop', 'POST', { session_id: sessionId });
      console.log('   ✅ Stream stopped');
    } catch (error) {
      console.log(`   ⚠️  Cleanup error: ${error.message}`);
    }
  }
}

async function main() {
  let sessionId = null;
  const results = {};

  try {
    // Test ROS2 connection
    results.ros2 = await testROS2Connection();
    
    if (results.ros2) {
      // Test real-time streaming
      sessionId = await testRealtimeStream();
      results.stream = !!sessionId;
      
      if (sessionId) {
        // Test WebSocket data
        results.websocket = await testWebSocketData(8000); // 8 seconds
      }
      
      // Test map save
      results.mapSave = await testMapSave();
    }

  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
  } finally {
    await cleanup(sessionId);
  }

  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 FINAL RESULTS');
  console.log('=' .repeat(60));
  
  const tests = [
    { name: 'ROS2 Connection', status: !!results.ros2, icon: '🔗' },
    { name: 'Real-time Stream', status: !!results.stream, icon: '📡' },
    { name: 'WebSocket Data', status: !!results.websocket, icon: '🔌' },
    { name: 'Map Save Button', status: !!results.mapSave, icon: '🗺️' }
  ];
  
  tests.forEach(test => {
    const status = test.status ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.icon} ${status} ${test.name}`);
  });
  
  const allPassed = tests.every(test => test.status);
  console.log(`\n🎯 Overall: ${allPassed ? '🎉 ALL SYSTEMS GO!' : '⚠️  Some issues detected'}`);
  
  if (allPassed) {
    console.log('\n🚀 READY FOR FRONTEND INTEGRATION!');
    console.log('');
    console.log('Frontend Integration Guide:');
    console.log('1. WebSocket: ws://localhost:3001/realtime-robot-data');
    console.log('2. Save Map: POST /api/v1/robots/map/save');
    console.log('3. API Docs: http://localhost:3001/api/docs');
    console.log('4. Events: robot_data, map_data, connection');
  }
}

if (require.main === module) {
  main();
}
