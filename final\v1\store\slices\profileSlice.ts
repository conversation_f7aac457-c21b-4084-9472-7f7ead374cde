/**
 * Profile Redux Slice
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { profileService, UserProfile } from '../../services/profile.service';
import { uploadService } from '../../services/upload.service';

// Async thunks for API calls
export const fetchProfile = createAsyncThunk(
  'profile/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const profile = await profileService.fetchProfile();
      return profile;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch profile');
    }
  }
);

export const updateProfile = createAsyncThunk(
  'profile/updateProfile',
  async (profileData: Partial<UserProfile>, { rejectWithValue }) => {
    try {
      const updatedProfile = await profileService.updateProfile(profileData);
      return updatedProfile;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update profile');
    }
  }
);

export const uploadProfilePhoto = createAsyncThunk(
  'profile/uploadProfilePhoto',
  async (file: File, { rejectWithValue }) => {
    try {
      const photoUrl = await uploadService.uploadProfilePhoto(file);
      return photoUrl;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to upload photo');
    }
  }
);

// Profile state interface
export interface ProfileState {
  // Profile data
  userProfile: UserProfile | null;
  currentProfilePicture: string;
  
  // Loading states
  isLoading: boolean;
  isUploadingPhoto: boolean;
  isUpdatingProfile: boolean;
  
  // Error states
  error: string | null;
  uploadError: string | null;
  updateError: string | null;
  
  // UI states
  lastFetchTime: number | null;
}

// Initial state
const initialState: ProfileState = {
  userProfile: null,
  currentProfilePicture: '',
  isLoading: false,
  isUploadingPhoto: false,
  isUpdatingProfile: false,
  error: null,
  uploadError: null,
  updateError: null,
  lastFetchTime: null,
};

// Profile slice
export const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    // Clear errors
    clearErrors: (state) => {
      state.error = null;
      state.uploadError = null;
      state.updateError = null;
    },
    
    // Clear upload error specifically
    clearUploadError: (state) => {
      state.uploadError = null;
    },
    
    // Clear update error specifically
    clearUpdateError: (state) => {
      state.updateError = null;
    },
    
    // Reset profile state
    resetProfile: (state) => {
      return initialState;
    },
    
    // Update current profile picture URL (for immediate UI feedback)
    updateCurrentProfilePicture: (state, action: PayloadAction<string>) => {
      state.currentProfilePicture = action.payload;
    },

    // Set upload error manually (for validation errors)
    setUploadError: (state, action: PayloadAction<string>) => {
      state.uploadError = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch profile
    builder
      .addCase(fetchProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userProfile = action.payload;
        state.currentProfilePicture = action.payload.picture || '';
        state.lastFetchTime = Date.now();
        state.error = null;
      })
      .addCase(fetchProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update profile
    builder
      .addCase(updateProfile.pending, (state) => {
        state.isUpdatingProfile = true;
        state.updateError = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isUpdatingProfile = false;
        state.userProfile = action.payload;
        state.updateError = null;
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isUpdatingProfile = false;
        state.updateError = action.payload as string;
      });

    // Upload profile photo
    builder
      .addCase(uploadProfilePhoto.pending, (state) => {
        state.isUploadingPhoto = true;
        state.uploadError = null;
      })
      .addCase(uploadProfilePhoto.fulfilled, (state, action) => {
        state.isUploadingPhoto = false;
        state.currentProfilePicture = action.payload;
        // Update the profile picture in the user profile if it exists
        if (state.userProfile) {
          state.userProfile.picture = action.payload;
        }
        state.uploadError = null;
      })
      .addCase(uploadProfilePhoto.rejected, (state, action) => {
        state.isUploadingPhoto = false;
        state.uploadError = action.payload as string;
      });
  },
});

// Export actions
export const {
  clearErrors,
  clearUploadError,
  clearUpdateError,
  resetProfile,
  updateCurrentProfilePicture,
  setUploadError,
} = profileSlice.actions;

// Export selectors
export const selectProfile = (state: any) => state.profile.userProfile;
export const selectCurrentProfilePicture = (state: any) => state.profile.currentProfilePicture;
export const selectIsLoading = (state: any) => state.profile.isLoading;
export const selectIsUploadingPhoto = (state: any) => state.profile.isUploadingPhoto;
export const selectIsUpdatingProfile = (state: any) => state.profile.isUpdatingProfile;
export const selectError = (state: any) => state.profile.error;
export const selectUploadError = (state: any) => state.profile.uploadError;
export const selectUpdateError = (state: any) => state.profile.updateError;
export const selectLastFetchTime = (state: any) => state.profile.lastFetchTime;

// Export reducer
export default profileSlice.reducer;
