/**
 * Redux Slices Export File
 */

// Auth slice
export { authSlice } from './authSlice';
export type { AuthState } from './authSlice';
export {
  loginUser,
  registerUser,
  logoutUser,
  verifyToken,
  clearErrors as clearAuthErrors,
  clearLoginError,
  clearRegisterError,
  resetAuth,
  setSessionExpiry,
  updateUser,
  selectAuth,
  selectUser,
  selectIsAuthenticated,
  selectIsLoggingIn,
  selectIsRegistering,
  selectIsLoggingOut,
  selectIsVerifying,
  selectAuthError,
  selectLoginError,
  selectRegisterError,
  selectLastLoginTime,
  selectSessionExpiry,
} from './authSlice';

// Profile slice
export { profileSlice } from './profileSlice';
export type { ProfileState } from './profileSlice';
export {
  fetchProfile,
  updateProfile,
  uploadProfilePhoto,
  clearErrors as clearProfileErrors,
  clearUploadError,
  clearUpdateError,
  resetProfile,
  updateCurrentProfilePicture,
  setUploadError,
  selectProfile,
  selectCurrentProfilePicture,
  selectIsLoading as selectProfileIsLoading,
  selectIsUploadingPhoto,
  selectIsUpdatingProfile,
  selectError as selectProfileError,
  selectUploadError,
  selectUpdateError,
  selectLastFetchTime,
} from './profileSlice';

// UI slice
export { uiSlice } from './uiSlice';
export type { UIState, Toast, Modal, LoadingState } from './uiSlice';
export {
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapsed,
  setSidebarCollapsed,
  setIsMobile,
  addToast,
  removeToast,
  clearToasts,
  openModal,
  closeModal,
  closeAllModals,
  setLoading,
  clearLoading,
  clearAllLoading,
  setCurrentPage,
  setPageTitle,
  setBreadcrumbs,
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  removeNotification,
  clearNotifications,
  setSearchQuery,
  setSearchResults,
  setIsSearching,
  clearSearch,
  selectTheme,
  selectSidebarOpen,
  selectSidebarCollapsed,
  selectIsMobile,
  selectToasts,
  selectModals,
  selectLoading,
  selectIsLoading,
  selectCurrentPage,
  selectPageTitle,
  selectBreadcrumbs,
  selectNotifications,
  selectUnreadNotifications,
  selectSearchQuery,
  selectSearchResults,
  selectIsSearching,
} from './uiSlice';
