import { OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { StartRealtimeStreamDto, StopRealtimeStreamDto, RealtimeEventData, RealtimeEventType } from '../dto/realtime-data.dto';
import { RealtimeDataService } from '../services/realtime-data.service';
export declare class RealtimeDataGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
    private readonly realtimeDataService;
    server: Server;
    private readonly logger;
    private connectedClients;
    private clientStreams;
    constructor(realtimeDataService: RealtimeDataService);
    afterInit(server: Server): void;
    handleConnection(client: Socket): void;
    handleDisconnect(client: Socket): void;
    handleStartStream(data: StartRealtimeStreamDto, client: Socket): Promise<void>;
    handleStopStream(data: StopRealtimeStreamDto, client: Socket): Promise<void>;
    handleGetStreamStatus(client: Socket): void;
    handleSubscribeToStream(data: {
        session_id: string;
    }, client: Socket): void;
    private broadcastToSubscribers;
    broadcastData(eventType: RealtimeEventType, data: RealtimeEventData): void;
    getConnectedClientsCount(): number;
    getClientStreams(clientId: string): string[];
}
