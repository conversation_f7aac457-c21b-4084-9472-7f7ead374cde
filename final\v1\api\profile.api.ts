/**
 * Profile API - Handles user profile related API calls
 */

import { BaseAPI, ApiResponse } from './base';

export interface UserProfile {
  userId: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  language: string;
  jobTitle: {
    id: string;
    name: string;
    description: string;
  };
  department: {
    id: string;
    name: string;
    description: string;
  };
  picture: string;
  lastLogin: string | null;
}

export interface ProfileUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  language?: string;
  jobTitle?: {
    id: string;
    name: string;
    description: string;
  };
  department?: {
    id: string;
    name: string;
    description: string;
  };
}

class ProfileAPI extends BaseAPI {
  private getAuthHeaders(): Record<string, string> {
    const customToken = localStorage.getItem('customToken');
    const authToken = localStorage.getItem('authToken');
    const token = customToken || authToken;
    
    return {
      'Authorization': token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json',
    };
  }

  /**
   * Fetch user profile
   */
  async fetchProfile(): Promise<ApiResponse<UserProfile>> {
    try {
      const response = await this.request<UserProfile>('/api/v1/profile', {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch profile',
      };
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: ProfileUpdateData): Promise<ApiResponse<UserProfile>> {
    try {
      const response = await this.request<UserProfile>('/api/v1/profile', {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData),
      });
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update profile',
      };
    }
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(file: File): Promise<ApiResponse<{ photoUrl: string }>> {
    try {
      const formData = new FormData();
      formData.append('profilePicture', file);

      const { customToken, authToken } = this.getAuthTokens();
      const token = customToken || authToken;

      const response = await fetch(`${this.baseURL}/api/v1/profile/upload-picture`, {
        method: 'POST',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to upload picture');
      }

      return {
        success: true,
        data: { photoUrl: data.photoUrl },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to upload profile picture',
      };
    }
  }

  private getAuthTokens() {
    return {
      customToken: localStorage.getItem('customToken'),
      authToken: localStorage.getItem('authToken'),
    };
  }
}

// Export singleton instance
export const profileAPI = new ProfileAPI();
export default profileAPI;
