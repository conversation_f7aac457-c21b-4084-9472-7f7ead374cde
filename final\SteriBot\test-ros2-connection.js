#!/usr/bin/env node
/**
 * Test script to verify ROS2 connection to *************:9090
 * This script tests the connection and basic data retrieval
 */

const WebSocket = require('ws');

const ROS2_IP = '*************';
const ROS2_PORT = 9090;
const WEBSOCKET_URL = `ws://${ROS2_IP}:${ROS2_PORT}`;

console.log('🤖 SteriBot ROS2 Connection Test');
console.log('=' .repeat(50));
console.log(`Target: ${WEBSOCKET_URL}`);
console.log('Testing: WebSocket connection and basic topic subscription');
console.log('');

async function testROS2Connection() {
  return new Promise((resolve, reject) => {
    console.log('🔗 Attempting to connect...');
    
    const ws = new WebSocket(WEBSOCKET_URL, {
      handshakeTimeout: 5000,
      skipUTF8Validation: true
    });

    let connectionSuccessful = false;
    let messagesReceived = 0;
    const startTime = Date.now();

    // Connection timeout
    const timeout = setTimeout(() => {
      if (!connectionSuccessful) {
        ws.close();
        reject(new Error('Connection timeout after 5 seconds'));
      }
    }, 5000);

    ws.on('open', () => {
      connectionSuccessful = true;
      clearTimeout(timeout);
      console.log('✅ Connected to ROS2 bridge!');
      console.log('📡 Subscribing to test topics...');

      // Subscribe to some basic topics to test data flow
      const subscriptions = [
        { topic: '/odom', type: 'nav_msgs/Odometry' },
        { topic: '/battery_state', type: 'sensor_msgs/BatteryState' },
        { topic: '/map', type: 'nav_msgs/OccupancyGrid' }
      ];

      subscriptions.forEach(sub => {
        const subscribeMsg = {
          op: 'subscribe',
          topic: sub.topic,
          type: sub.type,
          throttle_rate: 1000 // 1 Hz for testing
        };
        
        ws.send(JSON.stringify(subscribeMsg));
        console.log(`   📡 Subscribed to: ${sub.topic} (${sub.type})`);
      });

      // Test for 10 seconds then close
      setTimeout(() => {
        console.log(`\n📊 Test Results:`);
        console.log(`   Connection: ✅ Successful`);
        console.log(`   Duration: ${((Date.now() - startTime) / 1000).toFixed(1)}s`);
        console.log(`   Messages received: ${messagesReceived}`);
        console.log(`   Status: ${messagesReceived > 0 ? '✅ Data flowing' : '⚠️  No data received'}`);
        
        ws.close();
        resolve({
          success: true,
          messagesReceived,
          duration: Date.now() - startTime
        });
      }, 10000);
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        messagesReceived++;
        
        if (messagesReceived <= 3) {
          console.log(`📨 Message ${messagesReceived}: ${message.topic || 'unknown topic'}`);
        } else if (messagesReceived === 4) {
          console.log(`📨 ... (${messagesReceived} messages received, continuing silently)`);
        }
      } catch (e) {
        // Ignore non-JSON messages
      }
    });

    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.log('❌ Connection failed:', error.message);
      reject(error);
    });

    ws.on('close', (code, reason) => {
      clearTimeout(timeout);
      if (connectionSuccessful) {
        console.log('🔌 Connection closed gracefully');
      } else {
        reject(new Error(`Connection closed with code ${code}: ${reason}`));
      }
    });
  });
}

async function main() {
  try {
    const result = await testROS2Connection();
    
    console.log('\n🎯 Test Summary:');
    console.log('✅ ROS2 connection test PASSED');
    console.log(`📊 ${result.messagesReceived} messages received in ${(result.duration / 1000).toFixed(1)}s`);
    console.log('\n💡 Next steps:');
    console.log('1. Start real-time streaming via API: POST /api/v1/robots/realtime/start');
    console.log('2. Connect to WebSocket: ws://localhost:3001/realtime-robot-data');
    console.log('3. Test map export: POST /api/v1/robots/map/export');
    
    process.exit(0);
  } catch (error) {
    console.log('\n❌ Test Summary:');
    console.log('❌ ROS2 connection test FAILED');
    console.log(`Error: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Verify ROS2 system is running at *************:9090');
    console.log('2. Check network connectivity');
    console.log('3. Ensure rosbridge_server is running on the robot');
    console.log('4. Try: ros2 launch rosbridge_server rosbridge_websocket_launch.xml');
    
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testROS2Connection };
