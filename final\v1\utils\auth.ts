// utils/auth.ts

export interface LoginResponse {
  message: string;
  customToken: string;
  instructions: string;
  user: {
    userId: string;
    email: string;
    username: string;
    role: string;
  };
}

export interface ConnectionStatus {
  ip: string;
  status: string;
}

/**
 * Handle login response and save authentication data to localStorage
 * @param loginData - The login response data
 */
export const handleLoginResponse = (loginData: LoginResponse): void => {
  try {
    if (loginData && loginData.user && loginData.customToken) {
      // Save essential auth data
      localStorage.setItem('userId', loginData.user.userId);
      localStorage.setItem('customToken', loginData.customToken);

      // Save additional user data
      localStorage.setItem('userEmail', loginData.user.email);
      localStorage.setItem('username', loginData.user.username);
      localStorage.setItem('userRole', loginData.user.role);

      console.log('Login data saved to localStorage successfully');
      console.log('Saved userId:', loginData.user.userId);
      console.log('Saved customToken length:', loginData.customToken.length);
    } else {
      console.error('Invalid login data structure:', loginData);
    }
  } catch (error) {
    console.error('Error saving login data to localStorage:', error);
  }
};

/**
 * Save robot IP addresses to localStorage
 * @param ips - Array of IP connection statuses
 */
export const saveRobotIPs = (ips: ConnectionStatus[]): void => {
  if (ips && ips.length > 0) {
    localStorage.setItem('robotIPs', JSON.stringify(ips));
    // Only log when IPs actually change (removed spam logging)
  }
};

/**
 * Get saved robot IPs from localStorage
 * @returns Array of saved IP connection statuses
 */
export const getSavedRobotIPs = (): ConnectionStatus[] => {
  try {
    const saved = localStorage.getItem('robotIPs');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Error parsing saved robot IPs:', error);
    return [];
  }
};

/**
 * Get saved user authentication data from localStorage
 * @returns Object containing user auth data
 */
export const getSavedAuthData = () => {
  return {
    userId: localStorage.getItem('userId'),
    customToken: localStorage.getItem('customToken'),
    userEmail: localStorage.getItem('userEmail'),
    username: localStorage.getItem('username'),
    userRole: localStorage.getItem('userRole'),
  };
};

/**
 * Clear all saved authentication and robot data from localStorage
 */
export const clearSavedData = (): void => {
  localStorage.removeItem('userId');
  localStorage.removeItem('customToken');
  localStorage.removeItem('userEmail');
  localStorage.removeItem('username');
  localStorage.removeItem('userRole');
  localStorage.removeItem('robotIPs');
  console.log('All saved data cleared from localStorage');
};

/**
 * Check if user is authenticated (has valid tokens)
 * @returns boolean indicating if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const userId = localStorage.getItem('userId');
  const customToken = localStorage.getItem('customToken');
  return !!(userId && customToken);
};

/**
 * Test function to check what's available in localStorage
 * Call this from browser console: checkLocalStorage()
 */
export const checkLocalStorage = () => {
  console.log('🔍 Checking localStorage for authentication data...\n');

  // Check for our specific keys
  const customToken = localStorage.getItem('customToken');
  const userId = localStorage.getItem('userId');
  const userEmail = localStorage.getItem('userEmail');
  const username = localStorage.getItem('username');
  const userRole = localStorage.getItem('userRole');
  const robotIPs = localStorage.getItem('robotIPs');

  console.log('📋 Authentication Data Status:');
  console.log('customToken:', customToken ? '✅ FOUND' : '❌ NOT FOUND');
  if (customToken) {
    console.log('  → Length:', customToken.length, 'characters');
    console.log('  → Preview:', customToken.substring(0, 50) + '...');
  }

  console.log('userId:', userId ? '✅ FOUND' : '❌ NOT FOUND');
  if (userId) {
    console.log('  → Value:', userId);
  }

  console.log('userEmail:', userEmail ? '✅ FOUND' : '❌ NOT FOUND');
  if (userEmail) {
    console.log('  → Value:', userEmail);
  }

  console.log('username:', username ? '✅ FOUND' : '❌ NOT FOUND');
  if (username) {
    console.log('  → Value:', username);
  }

  console.log('userRole:', userRole ? '✅ FOUND' : '❌ NOT FOUND');
  if (userRole) {
    console.log('  → Value:', userRole);
  }

  console.log('robotIPs:', robotIPs ? '✅ FOUND' : '❌ NOT FOUND');
  if (robotIPs) {
    try {
      const parsed = JSON.parse(robotIPs);
      console.log('  → IPs:', parsed);
    } catch (e) {
      console.log('  → Raw value:', robotIPs);
    }
  }

  console.log('\n🔐 Authentication Status:', isAuthenticated() ? '✅ AUTHENTICATED' : '❌ NOT AUTHENTICATED');

  console.log('\n📦 All localStorage keys:', Object.keys(localStorage));

  return {
    customToken: !!customToken,
    userId: !!userId,
    userEmail: !!userEmail,
    username: !!username,
    userRole: !!userRole,
    robotIPs: !!robotIPs,
    isAuthenticated: isAuthenticated()
  };
};

/**
 * Test function to verify login data saving works
 * Call this from browser console to test: testLoginSave()
 */
export const testLoginSave = (): void => {
  const testLoginData: LoginResponse = {
    message: "Login successful",
    customToken: "test-token-123456789",
    instructions: "Test instructions",
    user: {
      userId: "test-user-id-123",
      email: "<EMAIL>",
      username: "testuser",
      role: "user"
    }
  };

  console.log('Testing login save with data:', testLoginData);
  handleLoginResponse(testLoginData);

  // Verify it was saved
  const saved = getSavedAuthData();
  console.log('Saved data verification:', saved);
};

/**
 * Save your specific login data to localStorage
 * Call this from browser console: saveYourLoginData()
 */
export const saveYourLoginData = () => {
  const yourLoginData = {
    customToken: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EsnKXxbb3W1VPrDshP4LQQxwI-2gTqFmTkNNZ8uQIxpdcXibgd5ehi-UwrmIEfL7OpD4XZMsXcdFYyV0fX3ExRvW7KitdIwuqcBAODoO4kuJba53DCf5pv-CC4xdNc89rBcQGyn3ZgEQZBBEUFbsCWtVkCRjyGqOs3H5foeIjbTxQsDhf05pxqoP6CWwt6K_gTQglEktF6zCwwatMtw3zKcdJuUIdBxrIB4J9g_ZzmvEH9dr6PrIVIf_fCTVxmichkDq_G5FInJuJrg1nYpPuPzVZiytGjpcG-Uu0qE2vAMDxBmaqs20gjqwE4O__dqmKRAhIZx2rNojzx1FDpNyOQ",
    userId: "btNUn3XDVgRtuEWa4K6MzPax7lk2"
  };

  // Save to localStorage
  localStorage.setItem('customToken', yourLoginData.customToken);
  localStorage.setItem('userId', yourLoginData.userId);

  console.log('✅ Your login data saved to localStorage:');
  console.log('📝 CustomToken saved (length):', yourLoginData.customToken.length);
  console.log('👤 UserId saved:', yourLoginData.userId);

  // Verify it was saved
  const savedToken = localStorage.getItem('customToken');
  const savedUserId = localStorage.getItem('userId');

  console.log('🔍 Verification:');
  console.log('Token saved correctly:', savedToken === yourLoginData.customToken);
  console.log('UserId saved correctly:', savedUserId === yourLoginData.userId);

  return {
    customToken: savedToken,
    userId: savedUserId
  };
};
