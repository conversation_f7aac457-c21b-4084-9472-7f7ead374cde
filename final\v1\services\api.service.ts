/**
 * API Service - Handles all HTTP API communications
 */

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AuthTokens {
  customToken?: string | null;
  authToken?: string | null;
}

export interface ConnectionTestResponse {
  success: boolean;
  connection_test: {
    status: string;
    has_position: boolean;
    has_battery: boolean;
    timestamp: string;
  };
}

export interface MapSaveResponse {
  success: boolean;
  filename: string;
  download_url: string;
  file_size: number;
  timestamp: string;
}

class ApiService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = 'http://localhost:3001/api/v1';
  }

  private getAuthTokens(): AuthTokens {
    return {
      customToken: localStorage.getItem('customToken'),
      authToken: localStorage.getItem('authToken')
    };
  }

  private getAuthHeaders(): Record<string, string> {
    const { customToken, authToken } = this.getAuthTokens();
    const token = customToken || authToken;
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: this.getAuthHeaders(),
        ...options
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API Error [${endpoint}]:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test ROS2 connection
   */
  async testConnection(): Promise<ApiResponse<ConnectionTestResponse>> {
    return this.makeRequest<ConnectionTestResponse>('/robots/test/connection', {
      method: 'POST'
    });
  }

  /**
   * Save current map
   */
  async saveMap(): Promise<ApiResponse<MapSaveResponse>> {
    return this.makeRequest<MapSaveResponse>('/robots/map/save', {
      method: 'POST'
    });
  }

  /**
   * Download map file with authentication
   */
  async downloadMapFile(downloadUrl: string, filename: string): Promise<void> {
    try {
      const token = localStorage.getItem('customToken');
      const fullUrl = `http://localhost:3001${downloadUrl}`;
      
      const response = await fetch(fullUrl, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Download failed: ${response.status} ${response.statusText}`);
      }
      
      // Create blob and trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download error:', error);
      throw new Error(`Failed to download file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
