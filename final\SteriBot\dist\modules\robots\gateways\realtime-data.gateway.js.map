{"version": 3, "file": "realtime-data.gateway.js", "sourceRoot": "", "sources": ["../../../../src/modules/robots/gateways/realtime-data.gateway.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,mDAS4B;AAC5B,2CAAwC;AACxC,yCAA2C;AAC3C,gEAMkC;AAClC,6EAAwE;AAUjE,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAU9B,YAA6B,mBAAwC;QAAxC,wBAAmB,GAAnB,mBAAmB,CAAqB;QAJpD,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;QACvD,qBAAgB,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC7C,kBAAa,GAAG,IAAI,GAAG,EAAuB,CAAC;IAEiB,CAAC;IAEzE,SAAS,CAAC,MAAc;QACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAGlE,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,SAA4B,EAAE,IAAuB,EAAE,EAAE;YAChG,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CAAC,MAAc;QAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;QAGpD,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE;YACxB,KAAK,EAAE,WAAW;YAClB,SAAS,EAAE,QAAQ;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,6CAA6C;SACvD,CAAC,CAAC;QAGH,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC5B,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,aAAa;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CAAC,MAAc;QAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,QAAQ,EAAE,CAAC,CAAC;QAGvD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,MAAM,SAAS,IAAI,eAAe,EAAE,CAAC;gBACxC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAGK,AAAN,KAAK,CAAC,iBAAiB,CACN,IAA4B,EACxB,MAAc;QAEjC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;YAGvG,MAAM,MAAM,GAAiB;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI;gBACvB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,IAAI,IAAI;gBACnD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,IAAI;gBAC/C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC;gBAC5C,UAAU,EAAE,UAAU,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;aAC3E,CAAC;YAGF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAElE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBAEnB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACxD,IAAI,aAAa,EAAE,CAAC;oBAClB,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACvC,CAAC;gBAGD,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBAC5B,KAAK,EAAE,gBAAgB;oBACvB,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,MAAM,EAAE,MAAM;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;YACzE,CAAC;iBAAM,CAAC;gBAEN,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;oBAC1B,KAAK,EAAE,cAAc;oBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC1B,KAAK,EAAE,cAAc;gBACrB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,gBAAgB,CACL,IAA2B,EACvB,MAAc;QAEjC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;YAElF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAG1E,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC5B,KAAK,EAAE,gBAAgB;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;aACvE,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC1B,KAAK,EAAE,cAAc;gBACrB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGD,qBAAqB,CAAoB,MAAc;QACrD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;YAClE,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;YAErE,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC3B,KAAK,EAAE,eAAe;gBACtB,cAAc,EAAE,aAAa;gBAC7B,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;gBACzC,YAAY,EAAE,aAAa,CAAC,MAAM;gBAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC1B,KAAK,EAAE,cAAc;gBACrB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGD,uBAAuB,CACN,IAA4B,EACxB,MAAc;QAEjC,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;YAG5B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,KAAK,EAAE,sBAAsB;gBAC7B,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,MAAM,CAAC,EAAE,0BAA0B,UAAU,EAAE,CAAC,CAAC;QAChF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC1B,KAAK,EAAE,cAAc;gBACrB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,SAA4B,EAAE,IAAuB;QAClF,IAAI,CAAC;YAEH,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACvD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAEvD,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAChF,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAGM,aAAa,CAAC,SAA4B,EAAE,IAAuB;QACxE,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAGM,wBAAwB;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACpC,CAAC;IAGM,gBAAgB,CAAC,QAAgB;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C,CAAC;CACF,CAAA;AAtPY,kDAAmB;AAI9B;IADC,IAAA,4BAAe,GAAE;8BACV,kBAAM;mDAAC;AA2DT;IADL,IAAA,6BAAgB,EAAC,cAAc,CAAC;IAE9B,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;qCADG,0CAAsB;QAChB,kBAAM;;4DAuDlC;AAGK;IADL,IAAA,6BAAgB,EAAC,aAAa,CAAC;IAE7B,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;qCADG,yCAAqB;QACf,kBAAM;;2DAgClC;AAGD;IADC,IAAA,6BAAgB,EAAC,mBAAmB,CAAC;IACf,WAAA,IAAA,4BAAe,GAAE,CAAA;;qCAAS,kBAAM;;gEAqBtD;AAGD;IADC,IAAA,6BAAgB,EAAC,qBAAqB,CAAC;IAErC,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;6CAAS,kBAAM;;kEA4BlC;8BAtNU,mBAAmB;IAR/B,IAAA,6BAAgB,EAAC;QAChB,IAAI,EAAE;YACJ,MAAM,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;YAC1D,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YACxB,WAAW,EAAE,IAAI;SAClB;QACD,SAAS,EAAE,sBAAsB;KAClC,CAAC;qCAWkD,2CAAmB;GAV1D,mBAAmB,CAsP/B"}