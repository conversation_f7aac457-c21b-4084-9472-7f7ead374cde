"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapExportHistoryDto = exports.BatchExportResponseDto = exports.BatchExportMapDto = exports.MapExportConfigDto = exports.ExportMapImageResponseDto = exports.ExportMapImageDto = exports.ColorScheme = exports.ImageFormat = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var ImageFormat;
(function (ImageFormat) {
    ImageFormat["PNG"] = "png";
    ImageFormat["JPEG"] = "jpeg";
    ImageFormat["JPG"] = "jpg";
})(ImageFormat || (exports.ImageFormat = ImageFormat = {}));
var ColorScheme;
(function (ColorScheme) {
    ColorScheme["GRAYSCALE"] = "grayscale";
    ColorScheme["COLORED"] = "colored";
    ColorScheme["HIGH_CONTRAST"] = "high_contrast";
    ColorScheme["CUSTOM"] = "custom";
})(ColorScheme || (exports.ColorScheme = ColorScheme = {}));
class ExportMapImageDto {
}
exports.ExportMapImageDto = ExportMapImageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address of the ROS2 bridge to get map data from',
        example: '*************',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIP)(),
    __metadata("design:type", String)
], ExportMapImageDto.prototype, "ip_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port of the ROS2 bridge (optional, defaults to 9090)',
        example: 9090,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ExportMapImageDto.prototype, "port", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Output image format',
        enum: ImageFormat,
        example: ImageFormat.PNG,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ImageFormat),
    __metadata("design:type", String)
], ExportMapImageDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Color scheme for the map visualization',
        enum: ColorScheme,
        example: ColorScheme.COLORED,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ColorScheme),
    __metadata("design:type", String)
], ExportMapImageDto.prototype, "color_scheme", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Scale factor for image size (1.0 = original resolution)',
        example: 2.0,
        minimum: 0.1,
        maximum: 10.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0.1),
    (0, class_validator_1.Max)(10.0),
    __metadata("design:type", Number)
], ExportMapImageDto.prototype, "scale_factor", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JPEG quality (only for JPEG format, 1-100)',
        example: 90,
        minimum: 1,
        maximum: 100,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ExportMapImageDto.prototype, "jpeg_quality", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Include grid lines in the exported image',
        example: false,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ExportMapImageDto.prototype, "include_grid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Include robot position marker if available',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ExportMapImageDto.prototype, "include_robot_position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Custom filename (without extension)',
        example: 'robot_map_2025_07_30',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExportMapImageDto.prototype, "filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Add timestamp to filename',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ExportMapImageDto.prototype, "add_timestamp", void 0);
class ExportMapImageResponseDto {
}
exports.ExportMapImageResponseDto = ExportMapImageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success status',
        example: true,
    }),
    __metadata("design:type", Boolean)
], ExportMapImageResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Generated filename',
        example: 'robot_map_2025_07_30_10_30_15.png',
    }),
    __metadata("design:type", String)
], ExportMapImageResponseDto.prototype, "filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File path relative to export directory',
        example: 'exports/maps/robot_map_2025_07_30_10_30_15.png',
    }),
    __metadata("design:type", String)
], ExportMapImageResponseDto.prototype, "file_path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File size in bytes',
        example: 245760,
    }),
    __metadata("design:type", Number)
], ExportMapImageResponseDto.prototype, "file_size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Image dimensions',
    }),
    __metadata("design:type", Object)
], ExportMapImageResponseDto.prototype, "dimensions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map metadata used for export',
    }),
    __metadata("design:type", Object)
], ExportMapImageResponseDto.prototype, "map_info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export timestamp',
        example: '2025-07-30T10:30:15.000Z',
    }),
    __metadata("design:type", String)
], ExportMapImageResponseDto.prototype, "exported_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Download URL for the exported file',
        example: '/api/robots/map/download/robot_map_2025_07_30_10_30_15.png',
    }),
    __metadata("design:type", String)
], ExportMapImageResponseDto.prototype, "download_url", void 0);
class MapExportConfigDto {
}
exports.MapExportConfigDto = MapExportConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Color configuration for different map cell types',
    }),
    __metadata("design:type", Object)
], MapExportConfigDto.prototype, "colors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Robot position marker configuration',
    }),
    __metadata("design:type", Object)
], MapExportConfigDto.prototype, "robot_marker", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Grid configuration',
    }),
    __metadata("design:type", Object)
], MapExportConfigDto.prototype, "grid", void 0);
class BatchExportMapDto {
}
exports.BatchExportMapDto = BatchExportMapDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of IP addresses to export maps from',
        type: [String],
        example: ['*************', '*************'],
    }),
    __metadata("design:type", Array)
], BatchExportMapDto.prototype, "ip_addresses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port for all connections (optional, defaults to 8765)',
        example: 8765,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], BatchExportMapDto.prototype, "port", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export configuration to apply to all maps',
        type: ExportMapImageDto,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], BatchExportMapDto.prototype, "export_config", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Create a ZIP archive of all exported maps',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BatchExportMapDto.prototype, "create_archive", void 0);
class BatchExportResponseDto {
}
exports.BatchExportResponseDto = BatchExportResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Overall success status',
        example: true,
    }),
    __metadata("design:type", Boolean)
], BatchExportResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of maps successfully exported',
        example: 2,
    }),
    __metadata("design:type", Number)
], BatchExportResponseDto.prototype, "exported_count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of maps that failed to export',
        example: 0,
    }),
    __metadata("design:type", Number)
], BatchExportResponseDto.prototype, "failed_count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Individual export results',
        type: [ExportMapImageResponseDto],
    }),
    __metadata("design:type", Array)
], BatchExportResponseDto.prototype, "results", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Archive information if created',
    }),
    __metadata("design:type", Object)
], BatchExportResponseDto.prototype, "archive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Batch export timestamp',
        example: '2025-07-30T10:30:15.000Z',
    }),
    __metadata("design:type", String)
], BatchExportResponseDto.prototype, "exported_at", void 0);
class MapExportHistoryDto {
}
exports.MapExportHistoryDto = MapExportHistoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export ID',
        example: 'export_1722336615000',
    }),
    __metadata("design:type", String)
], MapExportHistoryDto.prototype, "export_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address that was exported',
        example: '*************',
    }),
    __metadata("design:type", String)
], MapExportHistoryDto.prototype, "ip_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export filename',
        example: 'robot_map_2025_07_30_10_30_15.png',
    }),
    __metadata("design:type", String)
], MapExportHistoryDto.prototype, "filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export format used',
        enum: ImageFormat,
        example: ImageFormat.PNG,
    }),
    __metadata("design:type", String)
], MapExportHistoryDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File size in bytes',
        example: 245760,
    }),
    __metadata("design:type", Number)
], MapExportHistoryDto.prototype, "file_size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export timestamp',
        example: '2025-07-30T10:30:15.000Z',
    }),
    __metadata("design:type", String)
], MapExportHistoryDto.prototype, "exported_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether file still exists',
        example: true,
    }),
    __metadata("design:type", Boolean)
], MapExportHistoryDto.prototype, "file_exists", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Download URL if file exists',
        example: '/api/robots/map/download/robot_map_2025_07_30_10_30_15.png',
        required: false,
    }),
    __metadata("design:type", String)
], MapExportHistoryDto.prototype, "download_url", void 0);
//# sourceMappingURL=map-export.dto.js.map