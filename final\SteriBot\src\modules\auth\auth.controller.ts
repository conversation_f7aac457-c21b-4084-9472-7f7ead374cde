import { <PERSON>, Post, UseGuards, Get, Request, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from "@nestjs/swagger"
import { AuthService } from "./auth.service"
import { RegisterDto } from "./dto/register.dto"
import { LoginDto } from "./dto/login.dto"
import { UserProfileResponseDto } from "./dto/user-profile-response.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Authentication")
@Controller("auth")
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post("register")
  @ApiOperation({ summary: "Register a new user" })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto)
  }

  @Post("login")
  @ApiOperation({ summary: "Login user and get custom token" })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto)
  }

  

 

  @Get('profile')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'User profile retrieved successfully',
    type: UserProfileResponseDto
  })
  async getProfile(@Request() req: any): Promise<UserProfileResponseDto> {
    const user = await this.authService.validateUser(req.user.uid);
    await this.authService.updateLastLogin(req.user.uid);
    return user;
  }
}
