import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { 
  StartRealtimeStreamDto, 
  StopRealtimeStreamDto,
  RealtimeEventData,
  RealtimeEventType,
  StreamConfig
} from '../dto/realtime-data.dto';
import { RealtimeDataService } from '../services/realtime-data.service';

@WebSocketGateway({
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:3005'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: '/realtime-robot-data',
})
export class RealtimeDataGateway 
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect 
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(RealtimeDataGateway.name);
  private connectedClients = new Map<string, Socket>();
  private clientStreams = new Map<string, Set<string>>(); // clientId -> Set<sessionIds>

  constructor(private readonly realtimeDataService: RealtimeDataService) {}

  afterInit(server: Server) {
    this.logger.log('🚀 Realtime Data WebSocket Gateway initialized');
    
    // Set up service event listeners
    this.realtimeDataService.onDataReceived((eventType: RealtimeEventType, data: RealtimeEventData) => {
      this.broadcastToSubscribers(eventType, data);
    });
  }

  handleConnection(client: Socket) {
    const clientId = client.id;
    this.connectedClients.set(clientId, client);
    this.clientStreams.set(clientId, new Set());
    
    this.logger.log(`🔌 Client connected: ${clientId}`);
    
    // Send welcome message
    client.emit('connection', {
      event: 'connected',
      client_id: clientId,
      timestamp: new Date().toISOString(),
      message: 'Connected to SteriBot Realtime Data Gateway',
    });

    // Send current active streams
    const activeStreams = this.realtimeDataService.getActiveStreams();
    client.emit('active_streams', {
      event: 'active_streams',
      streams: activeStreams,
      timestamp: new Date().toISOString(),
    });
  }

  handleDisconnect(client: Socket) {
    const clientId = client.id;
    this.logger.log(`🔌 Client disconnected: ${clientId}`);
    
    // Stop all streams for this client
    const clientStreamIds = this.clientStreams.get(clientId);
    if (clientStreamIds) {
      for (const sessionId of clientStreamIds) {
        this.realtimeDataService.stopStream(sessionId);
      }
    }
    
    // Clean up
    this.connectedClients.delete(clientId);
    this.clientStreams.delete(clientId);
  }

  @SubscribeMessage('start_stream')
  async handleStartStream(
    @MessageBody() data: StartRealtimeStreamDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      this.logger.log(`📡 Starting stream for client ${client.id}: ${data.ip_address}:${data.port || 8765}`);
      
      // Create stream configuration
      const config: StreamConfig = {
        ip_address: data.ip_address,
        port: data.port || 8765,
        include_robot_data: data.include_robot_data ?? true,
        include_map_data: data.include_map_data ?? true,
        update_frequency: data.update_frequency || 2,
        session_id: `stream_${data.ip_address}_${data.port || 8765}_${Date.now()}`,
      };

      // Start the stream
      const result = await this.realtimeDataService.startStream(config);
      
      if (result.success) {
        // Track this stream for the client
        const clientStreams = this.clientStreams.get(client.id);
        if (clientStreams) {
          clientStreams.add(config.session_id);
        }

        // Send success response
        client.emit('stream_started', {
          event: 'stream_started',
          session_id: config.session_id,
          config: config,
          timestamp: new Date().toISOString(),
          success: true,
        });

        this.logger.log(`✅ Stream started successfully: ${config.session_id}`);
      } else {
        // Send error response
        client.emit('stream_error', {
          event: 'stream_error',
          error: result.error,
          timestamp: new Date().toISOString(),
          success: false,
        });

        this.logger.error(`❌ Failed to start stream: ${result.error}`);
      }
    } catch (error) {
      this.logger.error(`❌ Error starting stream: ${error.message}`);
      client.emit('stream_error', {
        event: 'stream_error',
        error: error.message,
        timestamp: new Date().toISOString(),
        success: false,
      });
    }
  }

  @SubscribeMessage('stop_stream')
  async handleStopStream(
    @MessageBody() data: StopRealtimeStreamDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      this.logger.log(`🛑 Stopping stream for client ${client.id}: ${data.session_id}`);
      
      const result = await this.realtimeDataService.stopStream(data.session_id);
      
      // Remove from client's tracked streams
      const clientStreams = this.clientStreams.get(client.id);
      if (clientStreams) {
        clientStreams.delete(data.session_id);
      }

      client.emit('stream_stopped', {
        event: 'stream_stopped',
        session_id: data.session_id,
        timestamp: new Date().toISOString(),
        success: result.success,
        message: result.success ? 'Stream stopped successfully' : result.error,
      });

      this.logger.log(`✅ Stream stopped: ${data.session_id}`);
    } catch (error) {
      this.logger.error(`❌ Error stopping stream: ${error.message}`);
      client.emit('stream_error', {
        event: 'stream_error',
        session_id: data.session_id,
        error: error.message,
        timestamp: new Date().toISOString(),
        success: false,
      });
    }
  }

  @SubscribeMessage('get_stream_status')
  handleGetStreamStatus(@ConnectedSocket() client: Socket) {
    try {
      const activeStreams = this.realtimeDataService.getActiveStreams();
      const clientStreams = this.clientStreams.get(client.id) || new Set();
      
      client.emit('stream_status', {
        event: 'stream_status',
        active_streams: activeStreams,
        client_streams: Array.from(clientStreams),
        total_active: activeStreams.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(`❌ Error getting stream status: ${error.message}`);
      client.emit('stream_error', {
        event: 'stream_error',
        error: error.message,
        timestamp: new Date().toISOString(),
        success: false,
      });
    }
  }

  @SubscribeMessage('subscribe_to_stream')
  handleSubscribeToStream(
    @MessageBody() data: { session_id: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const { session_id } = data;
      
      // Add client to stream subscribers
      const clientStreams = this.clientStreams.get(client.id);
      if (clientStreams) {
        clientStreams.add(session_id);
      }

      client.emit('subscribed_to_stream', {
        event: 'subscribed_to_stream',
        session_id: session_id,
        timestamp: new Date().toISOString(),
        success: true,
      });

      this.logger.log(`📡 Client ${client.id} subscribed to stream: ${session_id}`);
    } catch (error) {
      this.logger.error(`❌ Error subscribing to stream: ${error.message}`);
      client.emit('stream_error', {
        event: 'stream_error',
        error: error.message,
        timestamp: new Date().toISOString(),
        success: false,
      });
    }
  }

  private broadcastToSubscribers(eventType: RealtimeEventType, data: RealtimeEventData) {
    try {
      // Broadcast to all connected clients that are subscribed to this stream
      for (const [clientId, client] of this.connectedClients) {
        const clientStreams = this.clientStreams.get(clientId);

        if (clientStreams && 'session_id' in data && clientStreams.has(data.session_id)) {
          client.emit(eventType, data);
        }
      }
    } catch (error) {
      this.logger.error(`❌ Error broadcasting to subscribers: ${error.message}`);
    }
  }

  // Public method to broadcast data (called by service)
  public broadcastData(eventType: RealtimeEventType, data: RealtimeEventData) {
    this.broadcastToSubscribers(eventType, data);
  }

  // Get connected clients count
  public getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  // Get active streams for a client
  public getClientStreams(clientId: string): string[] {
    const streams = this.clientStreams.get(clientId);
    return streams ? Array.from(streams) : [];
  }
}
