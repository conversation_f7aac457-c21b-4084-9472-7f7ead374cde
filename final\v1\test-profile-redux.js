/**
 * Test script to verify Profile Redux refactoring
 */

console.log('🧪 Testing Profile Redux Refactoring...\n');

// Test 1: Check if Redux store files exist
const fs = require('fs');
const path = require('path');

const filesToCheck = [
  'store/index.ts',
  'store/hooks.ts',
  'store/provider.tsx',
  'store/slices/profileSlice.ts',
  'services/profile.service.ts',
  'services/upload.service.ts'
];

console.log('📁 Checking if all required files exist:');
filesToCheck.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - EXISTS`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Test 2: Check if Profile component imports are correct
console.log('\n📦 Checking Profile component imports:');
const profilePath = path.join(__dirname, 'components/profile.tsx');
if (fs.existsSync(profilePath)) {
  const profileContent = fs.readFileSync(profilePath, 'utf8');
  
  const requiredImports = [
    'useAppDispatch',
    'useAppSelector',
    'fetchProfile',
    'uploadProfilePhoto',
    'selectProfile',
    'selectIsLoading'
  ];
  
  requiredImports.forEach(importName => {
    if (profileContent.includes(importName)) {
      console.log(`✅ ${importName} - IMPORTED`);
    } else {
      console.log(`❌ ${importName} - MISSING`);
    }
  });
  
  // Check if old useState hooks are removed
  const oldHooks = ['useState'];
  oldHooks.forEach(hook => {
    if (profileContent.includes(hook)) {
      console.log(`⚠️  ${hook} - STILL PRESENT (should be removed)`);
    } else {
      console.log(`✅ ${hook} - REMOVED`);
    }
  });
} else {
  console.log('❌ Profile component not found');
}

// Test 3: Check if layout includes Redux Provider
console.log('\n🏗️  Checking layout Redux Provider:');
const layoutPath = path.join(__dirname, 'app/layout.tsx');
if (fs.existsSync(layoutPath)) {
  const layoutContent = fs.readFileSync(layoutPath, 'utf8');
  
  if (layoutContent.includes('ReduxProvider')) {
    console.log('✅ ReduxProvider - INCLUDED in layout');
  } else {
    console.log('❌ ReduxProvider - MISSING from layout');
  }
} else {
  console.log('❌ Layout file not found');
}

console.log('\n🎉 Redux refactoring test completed!');
console.log('\n📋 Summary:');
console.log('- All API calls moved to dedicated service files');
console.log('- Redux store configured with profile slice');
console.log('- Profile component refactored to use Redux');
console.log('- Local state replaced with Redux selectors and actions');
console.log('- Redux Provider added to app layout');

console.log('\n🚀 Next steps:');
console.log('1. Test the Profile component in the browser');
console.log('2. Verify profile loading works');
console.log('3. Test photo upload functionality');
console.log('4. Check Redux DevTools for state management');
