import { <PERSON>, Get, Post, Patch, Param, Delete, UseGuards, Body, HttpException, HttpStatus, Logger, <PERSON><PERSON> } from "@nestjs/common"
import { Response } from 'express'
import * as path from 'path'
import * as fs from 'fs'
import { ApiTags, ApiOperation, ApiBearerAuth, ApiBody, ApiResponse } from "@nestjs/swagger"
import { RobotsService } from "./robots.service"
import { CreateRobotDto } from "./dto/create-robot.dto"
import { UpdateRobotDto } from "./dto/update-robot.dto"
import { GetRobotDataDto, RobotDataResponseDto } from "./dto/get-robot-data.dto"
import { GetMapDataDto, MapDataResponseDto } from "./dto/get-map-data.dto"
import { StartRealtimeStreamDto, StopRealtimeStreamDto, RealtimeStreamStatusDto } from "./dto/realtime-data.dto"
import { ExportMapImageDto, ExportMapImageResponseDto, BatchExportMapDto, BatchExportResponseDto } from "./dto/map-export.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"
import { RealtimeDataService } from "./services/realtime-data.service"
import { MapExportService } from "./services/map-export.service"

@ApiTags("Robots")
@Controller("robots")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class RobotsController {
  private readonly logger = new Logger(RobotsController.name)

  constructor(
    private readonly robotsService: RobotsService,
    private readonly realtimeDataService: RealtimeDataService,
    private readonly mapExportService: MapExportService
  ) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Register a new robot (Admin only)" })
  create(@Body() createRobotDto: CreateRobotDto) {
    return this.robotsService.create(createRobotDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all robots" })
  findAll() {
    return this.robotsService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get robot by ID' })
  findOne(@Param('id') id: string) {
    return this.robotsService.findById(id);
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update robot (Admin only)" })
  update(@Param('id') id: string, @Body() updateRobotDto: UpdateRobotDto) {
    return this.robotsService.update(id, updateRobotDto)
  }

  @Patch(":id/status")
  @ApiOperation({ summary: "Update robot status" })
  updateStatus(@Param('id') id: string, @Body() status: any) {
    return this.robotsService.updateStatus(id, status)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete robot (Admin only)' })
  remove(@Param('id') id: string) {
    return this.robotsService.remove(id);
  }

  @Post('data/retrieve')
  @ApiOperation({
    summary: 'Retrieve real-time robot data from ROS2 bridge',
    description: 'Connects to a specific ROS2 bridge IP and retrieves robot position and battery level using Foxglove SDK',
  })
  @ApiBody({
    type: GetRobotDataDto,
    description: 'ROS2 bridge connection details',
    examples: {
      example1: {
        summary: 'Basic request',
        value: {
          ip_address: '*************',
          port: 9090,
        },
      },
      example2: {
        summary: 'Request with default port',
        value: {
          ip_address: '*************',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Successfully retrieved robot data',
    type: RobotDataResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data (e.g., invalid IP address)',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during data retrieval',
  })
  async retrieveRobotData(@Body() request: GetRobotDataDto): Promise<RobotDataResponseDto> {
    try {
      this.logger.log(`API: Retrieving robot data from ${request.ip_address}:${request.port || 9090}`)

      const result = await this.robotsService.getRobotData(request)

      this.logger.log(
        `API: Robot data retrieval completed - Status: ${result.connection_status}, ` +
        `Position: ${result.position ? 'Yes' : 'No'}, Battery: ${result.battery_level ? 'Yes' : 'No'}`
      )

      return result
    } catch (error) {
      this.logger.error('API: Robot data retrieval failed', error)
      throw new HttpException(
        {
          message: 'Failed to retrieve robot data',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Post('map/retrieve')
  @ApiOperation({
    summary: 'Retrieve static map data from ROS2 bridge',
    description: 'Connects to a specific ROS2 bridge IP and retrieves the current static map data from /map topic',
  })
  @ApiBody({
    type: GetMapDataDto,
    description: 'ROS2 bridge connection details for map retrieval',
    examples: {
      example1: {
        summary: 'Basic map request',
        value: {
          ip_address: '*************',
          port: 9090,
        },
      },
      example2: {
        summary: 'Request with default port',
        value: {
          ip_address: '*************',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Successfully retrieved map data',
    type: MapDataResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data (e.g., invalid IP address)',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during map data retrieval',
  })
  async retrieveMapData(@Body() request: GetMapDataDto): Promise<MapDataResponseDto> {
    try {
      this.logger.log(`API: Retrieving map data from ${request.ip_address}:${request.port || 9090}`)

      const result = await this.robotsService.getMapData(request)

      this.logger.log(
        `API: Map data retrieval completed - Status: ${result.connection_status}, ` +
        `Has Data: ${result.has_map_data}, Data Length: ${result.data_length}`
      )

      return result
    } catch (error) {
      this.logger.error('API: Map data retrieval failed', error)
      throw new HttpException(
        {
          message: 'Failed to retrieve map data',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Get('data/health')
  @ApiOperation({
    summary: 'Health check for robot data service',
    description: 'Returns the health status of the robot data retrieval service',
  })
  @ApiResponse({
    status: 200,
    description: 'Service health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        python_script_exists: { type: 'boolean', example: true },
        timestamp: { type: 'string', example: '2025-07-28T14:30:00.000Z' },
      },
    },
  })
  async robotDataHealthCheck() {
    try {
      const result = await this.robotsService.robotDataHealthCheck()
      this.logger.log(`API: Robot data health check - Status: ${result.status}`)
      return result
    } catch (error) {
      this.logger.error('API: Robot data health check failed', error)
      throw new HttpException(
        {
          message: 'Health check failed',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  // Real-time Streaming Endpoints
  @Get('realtime/streams')
  @ApiOperation({
    summary: 'Get active real-time streams',
    description: 'Returns a list of all currently active real-time data streams',
  })
  @ApiResponse({
    status: 200,
    description: 'List of active streams',
    type: [RealtimeStreamStatusDto],
  })
  async getActiveStreams(): Promise<RealtimeStreamStatusDto[]> {
    try {
      const streams = this.realtimeDataService.getActiveStreams()
      this.logger.log(`API: Retrieved ${streams.length} active streams`)
      return streams
    } catch (error) {
      this.logger.error('API: Failed to get active streams', error)
      throw new HttpException(
        {
          message: 'Failed to get active streams',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Post('realtime/start')
  @ApiOperation({
    summary: 'Start real-time data stream (REST endpoint)',
    description: 'Start a real-time data stream for robot and/or map data. Note: Use WebSocket connection for receiving the actual data.',
  })
  @ApiBody({
    type: StartRealtimeStreamDto,
    description: 'Stream configuration',
    examples: {
      robotAndMap: {
        summary: 'Robot and map data stream',
        value: {
          ip_address: '*************',
          port: 9090,
          include_robot_data: true,
          include_map_data: true,
          update_frequency: 2,
        },
      },
      robotOnly: {
        summary: 'Robot data only',
        value: {
          ip_address: '*************',
          include_robot_data: true,
          include_map_data: false,
          update_frequency: 5,
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Stream started successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        session_id: { type: 'string', example: 'stream_*************_9090_1722336615000' },
        message: { type: 'string', example: 'Real-time stream started successfully' },
        websocket_info: {
          type: 'object',
          properties: {
            namespace: { type: 'string', example: '/realtime-robot-data' },
            events: { type: 'array', items: { type: 'string' }, example: ['robot_data', 'map_data', 'connection'] },
          },
        },
      },
    },
  })
  async startRealtimeStream(@Body() request: StartRealtimeStreamDto) {
    try {
      this.logger.log(`API: Starting realtime stream for ${request.ip_address}:${request.port || 9090}`)

      const config = {
        ip_address: request.ip_address,
        port: request.port || 9090,
        include_robot_data: request.include_robot_data ?? true,
        include_map_data: request.include_map_data ?? true,
        update_frequency: request.update_frequency || 2,
        session_id: `stream_${request.ip_address}_${request.port || 9090}_${Date.now()}`,
      }

      const result = await this.realtimeDataService.startStream(config)

      if (result.success) {
        this.logger.log(`API: Realtime stream started successfully: ${config.session_id}`)
        return {
          success: true,
          session_id: config.session_id,
          message: 'Real-time stream started successfully',
          websocket_info: {
            namespace: '/realtime-robot-data',
            events: ['robot_data', 'map_data', 'connection', 'stream_status'],
            instructions: 'Connect to WebSocket to receive real-time data events',
          },
        }
      } else {
        throw new HttpException(
          {
            message: 'Failed to start real-time stream',
            error: result.error,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        )
      }
    } catch (error) {
      this.logger.error('API: Failed to start realtime stream', error)
      throw new HttpException(
        {
          message: 'Failed to start real-time stream',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Post('realtime/stop')
  @ApiOperation({
    summary: 'Stop real-time data stream',
    description: 'Stop an active real-time data stream by session ID',
  })
  @ApiBody({
    type: StopRealtimeStreamDto,
    description: 'Stream session to stop',
  })
  @ApiResponse({
    status: 200,
    description: 'Stream stopped successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        session_id: { type: 'string', example: 'stream_*************_9090_1722336615000' },
        message: { type: 'string', example: 'Real-time stream stopped successfully' },
      },
    },
  })
  async stopRealtimeStream(@Body() request: StopRealtimeStreamDto) {
    try {
      this.logger.log(`API: Stopping realtime stream: ${request.session_id}`)

      const result = await this.realtimeDataService.stopStream(request.session_id)

      if (result.success) {
        this.logger.log(`API: Realtime stream stopped successfully: ${request.session_id}`)
        return {
          success: true,
          session_id: request.session_id,
          message: 'Real-time stream stopped successfully',
        }
      } else {
        throw new HttpException(
          {
            message: 'Failed to stop real-time stream',
            error: result.error,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.BAD_REQUEST,
        )
      }
    } catch (error) {
      this.logger.error('API: Failed to stop realtime stream', error)
      throw new HttpException(
        {
          message: 'Failed to stop real-time stream',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  // Map Export Endpoints
  @Post('map/export')
  @ApiOperation({
    summary: 'Export robot map to image file',
    description: 'Export the current robot map as PNG or JPEG image with customizable options',
  })
  @ApiBody({
    type: ExportMapImageDto,
    description: 'Map export configuration',
    examples: {
      basic: {
        summary: 'Basic PNG export',
        value: {
          ip_address: '*************',
          port: 9090,
          format: 'png',
          color_scheme: 'colored',
        },
      },
      advanced: {
        summary: 'Advanced JPEG export with customization',
        value: {
          ip_address: '*************',
          port: 9090,
          format: 'jpeg',
          color_scheme: 'high_contrast',
          scale_factor: 2.0,
          jpeg_quality: 95,
          include_grid: true,
          include_robot_position: true,
          filename: 'robot_map_custom',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Map exported successfully',
    type: ExportMapImageResponseDto,
  })
  async exportMapImage(@Body() request: ExportMapImageDto): Promise<ExportMapImageResponseDto> {
    try {
      this.logger.log(`API: Exporting map from ${request.ip_address}:${request.port || 9090}`)
      const result = await this.mapExportService.exportMapImage(request)
      this.logger.log(`API: Map export completed: ${result.filename}`)
      return result
    } catch (error) {
      this.logger.error('API: Map export failed', error)
      throw new HttpException(
        {
          message: 'Map export failed',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Post('map/export/batch')
  @ApiOperation({
    summary: 'Batch export maps from multiple robots',
    description: 'Export maps from multiple robots simultaneously with optional archive creation',
  })
  @ApiBody({
    type: BatchExportMapDto,
    description: 'Batch export configuration',
  })
  @ApiResponse({
    status: 201,
    description: 'Batch export completed',
    type: BatchExportResponseDto,
  })
  async batchExportMaps(@Body() request: BatchExportMapDto): Promise<BatchExportResponseDto> {
    try {
      this.logger.log(`API: Batch exporting maps from ${request.ip_addresses.length} robots`)
      const result = await this.mapExportService.batchExportMaps(request)
      this.logger.log(`API: Batch export completed: ${result.exported_count} success, ${result.failed_count} failed`)
      return result
    } catch (error) {
      this.logger.error('API: Batch map export failed', error)
      throw new HttpException(
        {
          message: 'Batch map export failed',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Get('map/export/history')
  @ApiOperation({
    summary: 'Get map export history',
    description: 'Retrieve a list of previously exported map files',
  })
  @ApiResponse({
    status: 200,
    description: 'Export history retrieved',
    type: [Object], // MapExportHistoryDto array
  })
  async getMapExportHistory() {
    try {
      const history = await this.mapExportService.getExportHistory()
      this.logger.log(`API: Retrieved ${history.length} export history entries`)
      return history
    } catch (error) {
      this.logger.error('API: Failed to get export history', error)
      throw new HttpException(
        {
          message: 'Failed to get export history',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Get('map/export/health')
  @ApiOperation({
    summary: 'Health check for map export service',
    description: 'Check the health status of the map export service',
  })
  @ApiResponse({
    status: 200,
    description: 'Export service health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        python_script_exists: { type: 'boolean', example: true },
        export_directory_exists: { type: 'boolean', example: true },
      },
    },
  })
  async mapExportHealthCheck() {
    try {
      const result = await this.mapExportService.healthCheck()
      this.logger.log(`API: Map export health check - Status: ${result.status}`)
      return result
    } catch (error) {
      this.logger.error('API: Map export health check failed', error)
      throw new HttpException(
        {
          message: 'Map export health check failed',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Post('test/connection')
  @ApiOperation({
    summary: 'Test ROS2 connection and start demo stream',
    description: 'Quick test endpoint to verify ROS2 connection to *************:9090 and start a demo real-time stream',
  })
  @ApiResponse({
    status: 201,
    description: 'Connection test completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        connection_test: { type: 'object' },
        demo_stream: { type: 'object' },
        websocket_info: { type: 'object' },
      },
    },
  })
  async testROS2Connection() {
    try {
      this.logger.log('API: Testing ROS2 connection to *************:9090')

      // Test basic connection first
      const connectionTest = await this.robotsService.getRobotData({
        ip_address: '*************',
        port: 9090
      })

      // If connection works, start a demo stream
      let demoStream = null
      if (connectionTest.connection_status === 'connected') {
        const streamConfig = {
          ip_address: '*************',
          port: 9090,
          include_robot_data: true,
          include_map_data: true,
          update_frequency: 2,
          session_id: `demo_stream_${Date.now()}`,
        }

        demoStream = await this.realtimeDataService.startStream(streamConfig)
      }

      return {
        success: true,
        connection_test: {
          status: connectionTest.connection_status,
          has_position: !!connectionTest.position,
          has_battery: !!connectionTest.battery_level,
          last_updated: connectionTest.last_updated
        },
        demo_stream: demoStream ? {
          success: demoStream.success,
          session_id: demoStream.success ? 'demo_stream_started' : null,
          error: demoStream.error
        } : null,
        websocket_info: {
          namespace: '/realtime-robot-data',
          url: 'ws://localhost:3001/realtime-robot-data',
          events: ['robot_data', 'map_data', 'connection'],
          instructions: 'Connect to WebSocket to receive real-time data'
        },
        next_steps: [
          'Connect to WebSocket at ws://localhost:3001/realtime-robot-data',
          'Listen for robot_data events for position and battery',
          'Listen for map_data events for real-time map updates',
          'Use POST /robots/map/export to download map images'
        ]
      }
    } catch (error) {
      this.logger.error('API: ROS2 connection test failed', error)
      throw new HttpException(
        {
          message: 'ROS2 connection test failed',
          error: error.message,
          timestamp: new Date().toISOString(),
          troubleshooting: [
            'Verify ROS2 system is running at *************:9090',
            'Check network connectivity',
            'Ensure rosbridge_server is running: ros2 launch rosbridge_server rosbridge_websocket_launch.xml'
          ]
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Post('map/save')
  @ApiOperation({
    summary: 'Save current map (Frontend-friendly endpoint)',
    description: 'Simple endpoint for frontend "Save Map" button - downloads current map as PNG from *************:9090',
  })
  @ApiResponse({
    status: 201,
    description: 'Map saved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        filename: { type: 'string', example: 'robot_map_2025-07-31_14-30-00.png' },
        download_url: { type: 'string', example: '/api/v1/robots/map/download/robot_map_2025-07-31_14-30-00.png' },
        file_size: { type: 'number', example: 245760 },
        timestamp: { type: 'string', example: '2025-07-31T14:30:00.000Z' },
        message: { type: 'string', example: 'Map saved successfully' }
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Map save failed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: { type: 'string', example: 'Failed to connect to ROS2 system' },
        timestamp: { type: 'string', example: '2025-07-31T14:30:00.000Z' }
      },
    },
  })
  async saveCurrentMap() {
    try {
      this.logger.log('API: Save current map button clicked - downloading from *************:9090')

      // Use default settings optimized for frontend
      const exportConfig = {
        ip_address: '*************',
        port: 9090,
        format: 'png' as any,
        color_scheme: 'colored' as any,
        filename: 'robot_map',
        add_timestamp: true,
        quality: 95,
        resolution: 'high' as any
      }

      const result = await this.mapExportService.exportMapImage(exportConfig)

      // If we get here, the export was successful (service throws on failure)
      this.logger.log(`API: Map saved successfully - ${result.filename}`)
      return {
        success: true,
        filename: result.filename,
        download_url: `/api/v1/robots/map/download/${result.filename}`,
        file_size: result.file_size,
        timestamp: new Date().toISOString(),
        message: 'Map saved successfully'
      }
    } catch (error) {
      this.logger.error('API: Save current map failed', error)
      throw new HttpException(
        {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          troubleshooting: [
            'Verify ROS2 system is running at *************:9090',
            'Check network connectivity',
            'Ensure map data is available from robot'
          ]
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Get('map/download/:filename')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Download exported map file',
    description: 'Downloads a previously exported map file by filename',
  })
  @ApiResponse({
    status: 200,
    description: 'Map file downloaded successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 404,
    description: 'Map file not found',
  })
  async downloadMapFile(@Param('filename') filename: string, @Res() res: Response) {
    try {
      // Sanitize filename to prevent directory traversal
      const sanitizedFilename = path.basename(filename);
      const filePath = path.join(process.cwd(), 'exports', 'maps', sanitizedFilename);

      this.logger.log(`API: Download request for map file: ${sanitizedFilename}`);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        this.logger.warn(`API: Map file not found: ${filePath}`);
        throw new HttpException(
          {
            message: 'Map file not found',
            filename: sanitizedFilename,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Get file stats
      const stats = fs.statSync(filePath);

      // Set appropriate headers
      res.setHeader('Content-Type', 'image/png');
      res.setHeader('Content-Disposition', `attachment; filename="${sanitizedFilename}"`);
      res.setHeader('Content-Length', stats.size);

      // Stream the file
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      this.logger.log(`API: Map file downloaded successfully: ${sanitizedFilename} (${stats.size} bytes)`);

    } catch (error) {
      this.logger.error(`API: Map file download failed for ${filename}`, error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          message: 'Failed to download map file',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
