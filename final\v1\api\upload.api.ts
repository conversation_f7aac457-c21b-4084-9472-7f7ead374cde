/**
 * Upload API - Handles file upload operations
 */

import { BaseAPI, ApiResponse } from './base';

export interface PhotoUploadResponse {
  photoUrl: string;
  picture?: string;
  url?: string;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

class UploadAPI extends BaseAPI {
  private readonly maxFileSize: number = 5 * 1024 * 1024; // 5MB

  private getAuthTokens() {
    return {
      customToken: localStorage.getItem('customToken'),
      authToken: localStorage.getItem('authToken'),
      userId: localStorage.getItem('userId'),
    };
  }

  private getAuthHeaders(): Record<string, string> {
    const { customToken, authToken } = this.getAuthTokens();
    const token = customToken || authToken;
    
    if (!token) {
      throw new Error('No authentication token found');
    }

    return {
      'Authorization': `Bearer ${token}`,
      // Note: Don't set Content-Type for FormData, let browser set it with boundary
    };
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): FileValidationResult {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      return {
        isValid: false,
        error: 'Please select an image file',
      };
    }

    // Validate file size
    if (file.size > this.maxFileSize) {
      return {
        isValid: false,
        error: 'File size must be less than 5MB',
      };
    }

    return { isValid: true };
  }

  /**
   * Upload user profile photo
   */
  async uploadProfilePhoto(file: File): Promise<ApiResponse<PhotoUploadResponse>> {
    try {
      // Validate file first
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      const { userId } = this.getAuthTokens();
      if (!userId) {
        return {
          success: false,
          error: 'User ID not found',
        };
      }

      console.log('🔍 Upload Debug Info:');
      console.log('User ID:', userId);
      console.log('File name:', file.name);
      console.log('File size:', file.size);
      console.log('File type:', file.type);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('photo', file);

      // Log FormData contents
      console.log('📤 FormData contents:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const response = await fetch(`${this.baseURL}/api/v1/users/${userId}/photo`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: formData,
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

      // Try to get response text for debugging
      const responseText = await response.text();
      console.log('📥 Response body:', responseText);

      if (!response.ok) {
        // Try to parse error message from response
        let errorMessage = `Failed to upload photo: ${response.status}`;
        try {
          const errorData = JSON.parse(responseText);
          if (errorData.message || errorData.error) {
            errorMessage = errorData.message || errorData.error;
          }
        } catch (e) {
          // If response is not JSON, use the text as error message
          if (responseText) {
            errorMessage = responseText;
          }
        }
        return {
          success: false,
          error: errorMessage,
        };
      }

      // Parse the successful response
      const result = JSON.parse(responseText);
      console.log('✅ Upload successful:', result);

      // Extract photo URL from response
      const newPhotoUrl = result.photoUrl || result.picture || result.url;
      if (!newPhotoUrl) {
        console.warn('⚠️ Upload successful but no photo URL in response:', result);
        return {
          success: false,
          error: 'Upload successful but no photo URL received',
        };
      }

      console.log('✅ Photo uploaded successfully:', newPhotoUrl);
      return {
        success: true,
        data: {
          photoUrl: newPhotoUrl,
          picture: result.picture,
          url: result.url,
        },
      };
    } catch (error) {
      console.error('❌ Error uploading photo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to upload photo',
      };
    }
  }

  /**
   * Upload general file
   */
  async uploadFile(file: File, endpoint: string): Promise<ApiResponse<{ url: string }>> {
    try {
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || 'Failed to upload file',
        };
      }

      return {
        success: true,
        data: { url: data.url },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to upload file',
      };
    }
  }
}

// Export singleton instance
export const uploadAPI = new UploadAPI();
export default uploadAPI;
