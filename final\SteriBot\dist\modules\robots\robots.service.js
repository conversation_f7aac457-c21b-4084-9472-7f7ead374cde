"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RobotsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RobotsService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
const child_process_1 = require("child_process");
const path = require("path");
let RobotsService = RobotsService_1 = class RobotsService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "robots";
        this.logger = new common_1.Logger(RobotsService_1.name);
        this.pythonScriptPath = path.join(process.cwd(), 'ros_api', 'ros2_data_retriever.py');
        this.mapScriptPath = path.join(process.cwd(), 'ros_api', 'ros2_map_retriever.py');
        this.logger.log(`Python script path: ${this.pythonScriptPath}`);
        this.logger.log(`Map script path: ${this.mapScriptPath}`);
    }
    async create(createRobotDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc();
        const robotData = {
            ...createRobotDto,
            robotId: docRef.id,
            isConnected: false,
            createdAt: new Date(),
        };
        await docRef.set(robotData);
        return robotData;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`Robot with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async update(id, updateRobotDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            ...updateRobotDto,
            updatedAt: new Date(),
        });
        return this.findById(id);
    }
    async updateStatus(id, status) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            ...status,
            lastStatusUpdate: new Date(),
        });
        return this.findById(id);
    }
    async remove(id) {
        const firestore = this.firebaseService.getFirestore();
        await firestore.collection(this.collection).doc(id).delete();
        return { message: "Robot deleted successfully" };
    }
    async getRobotData(request) {
        const { ip_address, port = 9090 } = request;
        this.logger.log(`Retrieving robot data from ${ip_address}:${port}`);
        try {
            const result = await this.executePythonScript(ip_address, port);
            const response = {
                ...result,
                queried_ip: ip_address,
                queried_port: port,
            };
            this.logger.log(`Robot data retrieval completed for ${ip_address}:${port} - Status: ${result.connection_status}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Failed to retrieve robot data from ${ip_address}:${port}`, error);
            return {
                position: undefined,
                battery_level: undefined,
                connection_status: 'failed',
                last_updated: new Date().toISOString(),
                error_message: `Failed to retrieve robot data: ${error.message}`,
                queried_ip: ip_address,
                queried_port: port,
                foxglove_enabled: false,
            };
        }
    }
    async executePythonScript(ipAddress, port) {
        return new Promise((resolve, reject) => {
            const args = [this.pythonScriptPath, ipAddress, port.toString()];
            const pythonProcess = (0, child_process_1.spawn)('python', args);
            let stdout = '';
            let stderr = '';
            pythonProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            pythonProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            pythonProcess.on('close', (code) => {
                if (code !== 0) {
                    this.logger.error(`Python script exited with code ${code}`);
                    this.logger.error(`stderr: ${stderr}`);
                    reject(new Error(`Python script failed with code ${code}: ${stderr}`));
                    return;
                }
                try {
                    const lines = stdout.split('\n');
                    let jsonStartIndex = -1;
                    for (let i = 0; i < lines.length; i++) {
                        if (lines[i].includes('RETRIEVAL RESULTS')) {
                            jsonStartIndex = i + 2;
                            break;
                        }
                    }
                    if (jsonStartIndex === -1) {
                        const result = JSON.parse(stdout);
                        resolve(result);
                        return;
                    }
                    const jsonLines = lines.slice(jsonStartIndex);
                    const jsonString = jsonLines.join('\n').trim();
                    if (jsonString) {
                        const result = JSON.parse(jsonString);
                        resolve(result);
                    }
                    else {
                        reject(new Error('No JSON data found in script output'));
                    }
                }
                catch (parseError) {
                    this.logger.error('Failed to parse Python script output', parseError);
                    this.logger.error(`stdout: ${stdout}`);
                    reject(new Error(`Failed to parse script output: ${parseError.message}`));
                }
            });
            pythonProcess.on('error', (error) => {
                this.logger.error('Failed to start Python script', error);
                reject(new Error(`Failed to start Python script: ${error.message}`));
            });
            setTimeout(() => {
                pythonProcess.kill();
                reject(new Error('Python script execution timeout (30s)'));
            }, 30000);
        });
    }
    async getMapData(request) {
        const { ip_address, port = 9090 } = request;
        this.logger.log(`Retrieving map data from ${ip_address}:${port}`);
        try {
            const result = await this.executeMapScript(ip_address, port);
            const response = {
                ...result,
                queried_ip: ip_address,
                queried_port: port,
            };
            this.logger.log(`Map data retrieval completed for ${ip_address}:${port} - Status: ${result.connection_status}, Has Data: ${result.has_map_data}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Failed to retrieve map data from ${ip_address}:${port}`, error);
            return {
                info: undefined,
                data: undefined,
                statistics: undefined,
                connection_status: 'failed',
                last_updated: new Date().toISOString(),
                error_message: `Failed to retrieve map data: ${error.message}`,
                queried_ip: ip_address,
                queried_port: port,
                has_map_data: false,
                data_length: 0,
                summary: undefined,
            };
        }
    }
    async executeMapScript(ipAddress, port) {
        return new Promise((resolve, reject) => {
            const args = [this.mapScriptPath, ipAddress, port.toString()];
            const pythonProcess = (0, child_process_1.spawn)('python', args);
            let stdout = '';
            let stderr = '';
            pythonProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            pythonProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            pythonProcess.on('close', (code) => {
                if (code !== 0) {
                    this.logger.error(`Map script exited with code ${code}`);
                    this.logger.error(`stderr: ${stderr}`);
                    reject(new Error(`Map script failed with code ${code}: ${stderr}`));
                    return;
                }
                try {
                    const lines = stdout.split('\n');
                    let jsonStartIndex = -1;
                    for (let i = 0; i < lines.length; i++) {
                        if (lines[i].includes('MAP RETRIEVAL RESULTS')) {
                            jsonStartIndex = i + 2;
                            break;
                        }
                    }
                    if (jsonStartIndex === -1) {
                        const result = JSON.parse(stdout);
                        resolve(result);
                        return;
                    }
                    const jsonLines = lines.slice(jsonStartIndex);
                    const jsonString = jsonLines.join('\n').trim();
                    if (jsonString) {
                        const result = JSON.parse(jsonString);
                        resolve(result);
                    }
                    else {
                        reject(new Error('No JSON data found in map script output'));
                    }
                }
                catch (parseError) {
                    this.logger.error('Failed to parse map script output', parseError);
                    this.logger.error(`stdout: ${stdout}`);
                    reject(new Error(`Failed to parse map script output: ${parseError.message}`));
                }
            });
            pythonProcess.on('error', (error) => {
                this.logger.error('Failed to start map script', error);
                reject(new Error(`Failed to start map script: ${error.message}`));
            });
            setTimeout(() => {
                pythonProcess.kill();
                reject(new Error('Map script execution timeout (45s)'));
            }, 45000);
        });
    }
    async robotDataHealthCheck() {
        const fs = require('fs');
        const scriptExists = fs.existsSync(this.pythonScriptPath);
        return {
            status: scriptExists ? 'healthy' : 'unhealthy',
            python_script_exists: scriptExists,
            timestamp: new Date().toISOString(),
        };
    }
};
exports.RobotsService = RobotsService;
exports.RobotsService = RobotsService = RobotsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], RobotsService);
//# sourceMappingURL=robots.service.js.map