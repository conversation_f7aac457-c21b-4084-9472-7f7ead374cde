<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Position Data</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; }
        .data { background: #fff; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #ddd; }
        .position { font-size: 24px; font-weight: bold; color: #007bff; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SteriBot Position Debug Tool</h1>
        
        <div id="connection-status" class="status error">
            ❌ Not Connected
        </div>
        
        <div class="data">
            <h3>📍 Current Position</h3>
            <div class="position" id="position-display">X: 0.00, Y: 0.00</div>
            <div id="position-details"></div>
        </div>
        
        <div class="data">
            <h3>🔋 Battery & Speed</h3>
            <div id="battery-speed-display">Battery: N/A | Speed: N/A</div>
        </div>
        
        <div class="data">
            <h3>📊 Data Statistics</h3>
            <div id="stats">
                Robot Data Messages: 0<br>
                Map Data Messages: 0<br>
                Last Update: Never
            </div>
        </div>
        
        <div class="data">
            <h3>📝 Raw Data Log</h3>
            <pre id="raw-log" style="height: 200px; overflow-y: scroll;"></pre>
        </div>
        
        <div class="data">
            <button onclick="startDebugStream()" id="start-btn">🚀 Start Debug Stream</button>
            <button onclick="clearLog()" style="margin-left: 10px;">🧹 Clear Log</button>
        </div>
    </div>

    <script>
        let socket = null;
        let robotDataCount = 0;
        let mapDataCount = 0;
        let lastUpdate = 'Never';
        
        const TOKEN = localStorage.getItem('customToken');
        
        function log(message) {
            const logElement = document.getElementById('raw-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, isSuccess = false) {
            const statusElement = document.getElementById('connection-status');
            statusElement.textContent = message;
            statusElement.className = `status ${isSuccess ? 'success' : 'error'}`;
        }
        
        function updatePosition(data) {
            const positionElement = document.getElementById('position-display');
            const detailsElement = document.getElementById('position-details');
            
            if (data && data.position) {
                const x = data.position.x || 0;
                const y = data.position.y || 0;
                const z = data.position.z || 0;
                
                positionElement.textContent = `X: ${x.toFixed(3)}, Y: ${y.toFixed(3)}`;
                detailsElement.innerHTML = `
                    Z: ${z.toFixed(3)}<br>
                    Timestamp: ${data.timestamp || 'N/A'}<br>
                    Source: ${data.position.source_topic || 'Unknown'}
                `;
            }
        }
        
        function updateBatterySpeed(data) {
            const element = document.getElementById('battery-speed-display');
            
            const battery = data.battery ? `${data.battery.percentage}%` : 'N/A';
            const speed = data.speed !== undefined ? `${data.speed.toFixed(2)} m/s` : 'N/A';
            
            element.textContent = `Battery: ${battery} | Speed: ${speed}`;
        }
        
        function updateStats() {
            const statsElement = document.getElementById('stats');
            statsElement.innerHTML = `
                Robot Data Messages: ${robotDataCount}<br>
                Map Data Messages: ${mapDataCount}<br>
                Last Update: ${lastUpdate}
            `;
        }
        
        function startDebugStream() {
            if (!TOKEN) {
                alert('Please set customToken in localStorage first!');
                return;
            }
            
            log('🔌 Connecting to Socket.IO...');
            updateStatus('🔌 Connecting...', false);
            
            socket = io('http://localhost:3001/realtime-robot-data', {
                auth: { token: TOKEN },
                query: { token: TOKEN }
            });
            
            socket.on('connect', () => {
                log('✅ Socket.IO connected!');
                updateStatus('✅ Connected - Starting Stream...', true);
                
                // Start stream
                socket.emit('start_stream', {
                    ip_address: '*************',
                    port: 9090,
                    include_robot_data: true,
                    include_map_data: true,
                    update_frequency: 2
                });
                log('🚀 Sent start_stream request');
            });
            
            socket.on('stream_started', (response) => {
                log(`📡 Stream started: ${JSON.stringify(response)}`);
                updateStatus('📡 Stream Active - Receiving Data', true);
            });
            
            socket.on('robot_data', (data) => {
                robotDataCount++;
                lastUpdate = new Date().toLocaleTimeString();
                
                log(`🤖 Robot Data #${robotDataCount}: ${JSON.stringify(data)}`);
                updatePosition(data);
                updateBatterySpeed(data);
                updateStats();
            });
            
            socket.on('map_data', (data) => {
                mapDataCount++;
                lastUpdate = new Date().toLocaleTimeString();
                
                log(`🗺️ Map Data #${mapDataCount}: ${data.info ? `${data.info.width}x${data.info.height}` : 'No info'}`);
                updateStats();
            });
            
            socket.on('connection', (data) => {
                log(`🔗 Connection event: ${JSON.stringify(data)}`);
            });
            
            socket.on('connect_error', (error) => {
                log(`❌ Connection error: ${error.message}`);
                updateStatus('❌ Connection Failed', false);
            });
            
            socket.on('disconnect', () => {
                log('🔌 Disconnected');
                updateStatus('🔌 Disconnected', false);
            });
            
            document.getElementById('start-btn').disabled = true;
        }
        
        function clearLog() {
            document.getElementById('raw-log').textContent = '';
            robotDataCount = 0;
            mapDataCount = 0;
            lastUpdate = 'Never';
            updateStats();
        }
        
        // Initialize
        if (TOKEN) {
            log(`🔑 Token found: ${TOKEN.substring(0, 20)}...`);
        } else {
            log('❌ No token found in localStorage');
            updateStatus('❌ No Authentication Token', false);
        }
        
        updateStats();
    </script>
</body>
</html>
