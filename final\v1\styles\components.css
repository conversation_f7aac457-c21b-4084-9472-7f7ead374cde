/**
 * Component Styles - Reusable component styling
 */

/* Button Components */
.connect-button {
  position: relative;
  overflow: hidden;
}

.connect-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #14b8a6, #0C6980, #14b8a6, #0C6980);
  background-size: 400% 400%;
  border-radius: 10px;
  z-index: -1;
  animation: gradientShift 3s ease infinite;
  opacity: 0.8;
}

.connect-button:hover::before {
  opacity: 1;
  animation-duration: 1.5s;
}

.start-scan-button {
  box-shadow: 0 10px 30px rgba(20, 184, 166, 0.2);
}

.start-scan-button:hover {
  box-shadow: 0 15px 40px rgba(20, 184, 166, 0.4);
}

/* Form Components */
.range-teal::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: linear-gradient(90deg, #14b8a6, #0C6980);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-teal::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: linear-gradient(90deg, #14b8a6, #0C6980);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-teal::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
}

.range-teal::-moz-range-track {
  height: 8px;
  border-radius: 4px;
}

/* Card Components */
.robot-glow {
  box-shadow: 0 0 30px rgba(20, 184, 166, 0.3);
}

.shadow-3d {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 15px 25px rgba(20, 184, 166, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Modal Components */
.modal-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.login-modal-3d {
  transform: perspective(1000px) rotateX(2deg) rotateY(-2deg);
  transition: transform 0.3s ease;
}

.login-modal-3d:hover {
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.02);
}

.network-modal-3d {
  transform: perspective(1000px) rotateX(-2deg) rotateY(2deg);
  transition: transform 0.3s ease;
}

.network-modal-3d:hover {
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.02);
}

.connecting-card-3d {
  transform: perspective(1000px) rotateX(1deg);
  transition: transform 0.3s ease;
}

.connecting-card-3d:hover {
  transform: perspective(1000px) rotateX(0deg) scale(1.01);
}

/* Text Components */
.gradient-text {
  background: linear-gradient(90deg, #0A3F4C, #0C6980, #14b8a6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientTextShift 3s ease infinite;
}

/* Layout Components */
.login-container {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
}

.login-background {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
  backdrop-filter: blur(10px);
}

.connecting-container {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
}

.connecting-container-inverted {
  background: rgba(240, 253, 250, 0.95);
  backdrop-filter: blur(10px);
}

.connecting-background {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
  backdrop-filter: blur(15px);
}

.robot-interface-container {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
}

/* Utility Classes */
.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 2s ease infinite;
}
