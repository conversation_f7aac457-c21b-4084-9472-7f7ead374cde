// components/ActionButtons.tsx
import { useState } from 'react';
import { useRobotData } from '@/hooks/use-robot-data';
import { useToast } from '@/hooks/use-toast';

interface ActionButtonsProps {
  onMapSaved?: () => void;
  onReset?: () => void;
  isMapSavedState?: boolean;
}

export function ActionButtons({ onMapSaved, onReset }: ActionButtonsProps) {
  const [isScanning, setIsScanning] = useState(false); // Start with false so button shows "Start Scan"
  const [isMapSaved, setIsMapSaved] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { startStream, stopStream, saveMap } = useRobotData();
  const { toast } = useToast();

  const handleScanAction = async () => {
    if (!isScanning) {
      // Start scanning
      const success = await startStream();
      if (success) {
        setIsScanning(true); // Now scanning, button should show "Stop Scan"
        toast({
          title: "Real-time Stream Started",
          description: "Now receiving live robot data",
        });
      } else {
        toast({
          title: "Failed to Start Stream",
          description: "Please check your authentication and connection",
          variant: "destructive",
        });
      }
    } else {
      // Stop scanning and save the map
      await stopStream();
      setIsSaving(true);
      try {
        const result = await saveMap();
        if (result.success) {
          setIsMapSaved(true);
          setIsScanning(false); // Reset to initial state
          onMapSaved?.();
          toast({
            title: "Map Saved Successfully!",
            description: `Map saved as ${result.filename}`,
          });
          setTimeout(() => setIsMapSaved(false), 3000);
        } else {
          console.error('Failed to save map:', result.error);
          toast({
            title: "Failed to Save Map",
            description: result.error || "Unknown error occurred",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error saving map:', error);
        toast({
          title: "Error Saving Map",
          description: "Please check your connection and try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    }
  }

  const resetMap = async () => {
    // Stop streaming
    await stopStream();
    setIsScanning(false); // Reset to initial state (Start Scan)
    setIsMapSaved(false);
    setIsSaving(false);
    // Notify parent component to reset
    onReset?.();
  }

  return (
    <div className="space-y-2 flex flex-col">
      <button
        onClick={handleScanAction}
        disabled={isSaving}
        className={`w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 mt-auto transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ${isScanning ? '' : ''}`}
        style={{
          background: isScanning
            ? 'linear-gradient(90deg, #dc2626, #b91c1c)'
            : 'linear-gradient(90deg, #14b8a6, #0C6980)'
        }}
      >
        {!isScanning ? (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10" />
              <rect x="9" y="9" width="6" height="6" />
            </svg>
            Start Scan
          </>
        ) : (
          <>
            {isSaving ? (
              <svg className="animate-spin" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                <path d="M8 16H3v5" />
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                <path d="M21 3v5h-5" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="6" y="6" width="12" height="12" />
                <rect x="9" y="9" width="6" height="6" />
              </svg>
            )}
            {isSaving ? 'Saving Map...' : isMapSaved ? 'Map Saved!' : 'Stop Scan'}
          </>
        )}
      </button>

      <button
        onClick={resetMap}
        className="w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 transition-all hover:scale-105"
        style={{ background: 'linear-gradient(90deg, #14b8a6, #0C6980)' }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
          <path d="M21 3v5h-5" />
          <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
          <path d="M8 16H3v5" />
        </svg>
        Reset Map
      </button>
    </div>
  )
}