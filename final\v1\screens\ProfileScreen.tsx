/**
 * Profile Screen Component
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  User, 
  Mail, 
  Briefcase, 
  Building, 
  Camera, 
  Save,
  Loader2,
  CheckCircle
} from 'lucide-react';
import { 
  selectProfile, 
  selectIsUpdatingProfile,
  selectUpdateError,
  selectIsUploadingPhoto,
  selectUploadError,
  selectCurrentProfilePicture,
  fetchProfile,
  updateProfile,
  uploadProfilePhoto,
  clearUpdateError,
  clearUploadError,
  setCurrentPage,
  setPageTitle,
  setBreadcrumbs
} from '@/store/slices';
import { useUpload } from '@/hooks';

export interface ProfileScreenProps {
  className?: string;
}

export const ProfileScreen: React.FC<ProfileScreenProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { uploadProfilePhoto: uploadPhoto } = useUpload();

  const profile = useAppSelector(selectProfile);
  const isUpdating = useAppSelector(selectIsUpdatingProfile);
  const updateError = useAppSelector(selectUpdateError);
  const isUploadingPhoto = useAppSelector(selectIsUploadingPhoto);
  const uploadError = useAppSelector(selectUploadError);
  const currentProfilePicture = useAppSelector(selectCurrentProfilePicture);

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    language: '',
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Set page info
  useEffect(() => {
    dispatch(setCurrentPage('profile'));
    dispatch(setPageTitle('Profile'));
    dispatch(setBreadcrumbs([
      { label: 'Home', href: '/' },
      { label: 'Profile' }
    ]));
  }, [dispatch]);

  // Fetch profile data
  useEffect(() => {
    dispatch(fetchProfile());
  }, [dispatch]);

  // Update form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        email: profile.email || '',
        language: profile.language || '',
      });
    }
  }, [profile]);

  // Check for changes
  useEffect(() => {
    if (profile) {
      const hasFormChanges = 
        formData.firstName !== (profile.firstName || '') ||
        formData.lastName !== (profile.lastName || '') ||
        formData.email !== (profile.email || '') ||
        formData.language !== (profile.language || '');
      
      setHasChanges(hasFormChanges);
    }
  }, [formData, profile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      const result = await dispatch(updateProfile(formData));
      
      if (updateProfile.fulfilled.match(result)) {
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);
      }
    } catch (error) {
      console.error('Profile update error:', error);
    }
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const photoUrl = await uploadPhoto(file);
      if (photoUrl) {
        // The Redux store will be updated automatically by the uploadProfilePhoto action
        console.log('Photo uploaded successfully:', photoUrl);
      }
    } catch (error) {
      console.error('Photo upload error:', error);
    }
  };

  const clearErrors = () => {
    if (updateError) dispatch(clearUpdateError());
    if (uploadError) dispatch(clearUploadError());
  };

  if (!profile) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>
      </div>

      {/* Success Alert */}
      {showSuccess && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Profile updated successfully!
          </AlertDescription>
        </Alert>
      )}

      {/* Error Alerts */}
      {(updateError || uploadError) && (
        <Alert variant="destructive">
          <AlertDescription className="flex justify-between items-center">
            {updateError || uploadError}
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearErrors}
              className="h-auto p-1 text-xs"
            >
              ✕
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Picture Card */}
        <Card>
          <CardHeader>
            <CardTitle>Profile Picture</CardTitle>
            <CardDescription>
              Update your profile photo
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-4">
            <Avatar className="h-32 w-32">
              <AvatarImage 
                src={currentProfilePicture || profile.picture} 
                alt={`${profile.firstName} ${profile.lastName}`} 
              />
              <AvatarFallback className="text-2xl">
                {profile.firstName?.[0]}{profile.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex flex-col items-center space-y-2">
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
                id="photo-upload"
                disabled={isUploadingPhoto}
              />
              <Label htmlFor="photo-upload" className="cursor-pointer">
                <Button 
                  variant="outline" 
                  size="sm" 
                  disabled={isUploadingPhoto}
                  asChild
                >
                  <span>
                    {isUploadingPhoto ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Camera className="h-4 w-4 mr-2" />
                        Change Photo
                      </>
                    )}
                  </span>
                </Button>
              </Label>
              <p className="text-xs text-muted-foreground text-center">
                JPG, PNG or GIF. Max size 5MB.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Profile Information Card */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>
              Update your personal details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="Enter your first name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Enter your last name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Enter your email"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Input
                id="language"
                value={formData.language}
                onChange={(e) => handleInputChange('language', e.target.value)}
                placeholder="Enter your preferred language"
              />
            </div>

            <Separator />

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  if (profile) {
                    setFormData({
                      firstName: profile.firstName || '',
                      lastName: profile.lastName || '',
                      email: profile.email || '',
                      language: profile.language || '',
                    });
                  }
                }}
                disabled={!hasChanges || isUpdating}
              >
                Reset
              </Button>
              <Button
                onClick={handleSave}
                disabled={!hasChanges || isUpdating}
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
          <CardDescription>
            Read-only account details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="flex items-center space-x-3">
              <User className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Username</p>
                <p className="text-sm text-muted-foreground">{profile.username}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Briefcase className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Job Title</p>
                <p className="text-sm text-muted-foreground">
                  {profile.jobTitle?.name || 'Not specified'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Building className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Department</p>
                <p className="text-sm text-muted-foreground">
                  {profile.department?.name || 'Not specified'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Badge variant="secondary">{profile.role}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileScreen;
