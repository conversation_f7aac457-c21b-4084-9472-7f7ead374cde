// components/ui/StartSterilizationButton.tsx
interface StartSterilizationButtonProps {
  onClick: () => void;
}

export function StartSterilizationButton({ onClick }: StartSterilizationButtonProps) {
  return (
    <button
      onClick={onClick}
      className="w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 transition-all hover:scale-105"
      style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M8 6l4-4 4 4"/>
        <path d="M12 2v10.3a4 4 0 0 1-1.172 2.872L4 22"/>
        <path d="m20 22-6.828-6.828A4 4 0 0 1 12 12.3"/>
      </svg>
      Start Sterilization
    </button>
  );
}
