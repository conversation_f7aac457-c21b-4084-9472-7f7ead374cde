import { ExportMapImageDto, ExportMapImageResponseDto, BatchExportMapDto, BatchExportResponseDto, MapExportHistoryDto } from '../dto/map-export.dto';
export declare class MapExportService {
    private readonly logger;
    private readonly pythonScriptPath;
    private readonly exportDirectory;
    private readonly maxExportHistory;
    constructor();
    private ensureExportDirectory;
    exportMapImage(request: ExportMapImageDto): Promise<ExportMapImageResponseDto>;
    batchExportMaps(request: BatchExportMapDto): Promise<BatchExportResponseDto>;
    getExportHistory(): Promise<MapExportHistoryDto[]>;
    getExportFile(filename: string): Promise<{
        filePath: string;
        exists: boolean;
    }>;
    deleteExportFile(filename: string): Promise<boolean>;
    private executePythonScript;
    private createArchive;
    healthCheck(): Promise<{
        status: string;
        python_script_exists: boolean;
        export_directory_exists: boolean;
    }>;
}
