# Enhanced User Registration Test Guide

## Prerequisites
1. Your NestJS server is running
2. You have created departments and job titles (follow the previous guides)
3. You have the department and job title IDs ready

## Step 1: Get Available Departments and Job Titles

### Get Departments
```bash
GET /departments
Authorization: Bearer YOUR_ADMIN_TOKEN
```

### Get Job Titles
```bash
GET /job-titles
Authorization: Bearer YOUR_ADMIN_TOKEN
```

Save the IDs from these responses for the registration test.

## Step 2: Test Enhanced User Registration

### Complete User Registration with All Fields
```bash
POST /auth/register
Content-Type: application/json

{
  "username": "sarah_johnson",
  "firstName": "Sarah",
  "lastName": "<PERSON>",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "user",
  "language": "en",
  "jobTitleId": "YOUR_JOB_TITLE_ID_HERE",
  "departmentId": "YOUR_DEPARTMENT_ID_HERE",
  "picture": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
}
```

### Expected Success Response
```json
{
  "message": "User registered successfully",
  "userId": "generated_firebase_uid"
}
```

## Step 3: Test Job Title Validation

### Test with Invalid Job Title ID
```bash
POST /auth/register
Content-Type: application/json

{
  "username": "test_user",
  "firstName": "Test",
  "lastName": "User",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "jobTitleId": "invalid_job_title_id"
}
```

### Expected Error Response
```json
{
  "statusCode": 400,
  "message": "Job title with ID invalid_job_title_id not found",
  "error": "Bad Request"
}
```

## Step 4: Test Department Validation

### Test with Invalid Department ID
```bash
POST /auth/register
Content-Type: application/json

{
  "username": "test_user2",
  "firstName": "Test",
  "lastName": "User2",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "departmentId": "invalid_department_id"
}
```

### Expected Error Response
```json
{
  "statusCode": 400,
  "message": "Department with ID invalid_department_id not found",
  "error": "Bad Request"
}
```

## Step 5: Verify Enhanced Profile Response

After successful registration, login with the new user and check the profile:

### Login
```bash
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

### Get Enhanced Profile
```bash
GET /auth/profile
Authorization: Bearer USER_TOKEN_FROM_LOGIN
```

### Expected Enhanced Profile Response
```json
{
  "userId": "generated_firebase_uid",
  "username": "sarah_johnson",
  "firstName": "Sarah",
  "lastName": "Johnson",
  "email": "<EMAIL>",
  "role": "user",
  "language": "en",
  "jobTitle": {
    "id": "jt_123",
    "name": "Senior Software Engineer",
    "description": "Experienced software engineer responsible for leading development projects, mentoring junior developers, and architecting complex software solutions"
  },
  "department": {
    "id": "dept_123",
    "name": "Engineering",
    "description": "Software development and engineering team"
  },
  "picture": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
  "lastLogin": "2025-07-30T10:30:00.000Z"
}
```

## Step 6: Test Minimal Registration (Backward Compatibility)

### Registration with Only Required Fields
```bash
POST /auth/register
Content-Type: application/json

{
  "username": "minimal_user",
  "firstName": "Min",
  "lastName": "User",
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

This should work without jobTitleId, departmentId, or picture.

## Step 7: Additional Test Cases

### Test Case 1: Registration with Job Title but No Department
```bash
POST /auth/register
Content-Type: application/json

{
  "username": "partial_user",
  "firstName": "Partial",
  "lastName": "User",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "jobTitleId": "YOUR_JOB_TITLE_ID_HERE"
}
```

### Test Case 2: Registration with Department but No Job Title
```bash
POST /auth/register
Content-Type: application/json

{
  "username": "dept_user",
  "firstName": "Dept",
  "lastName": "User",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "departmentId": "YOUR_DEPARTMENT_ID_HERE"
}
```

## Validation Test Results

After running these tests, you should verify:

1. ✅ **Enhanced Fields**: firstName and lastName are required and included in responses
2. ✅ **Job Title Validation**: Invalid jobTitleId causes registration to fail with clear error
3. ✅ **Department Validation**: Invalid departmentId causes registration to fail with clear error
4. ✅ **Complete Profile**: Profile response includes all enhanced fields with populated job title and department information
5. ✅ **Backward Compatibility**: Registration works with minimal required fields
6. ✅ **Picture Support**: Profile picture URL is stored and returned in profile response
7. ✅ **Proper Error Handling**: Clear error messages for validation failures

## Sample Complete Test User Data

Here's a complete example with realistic data:

```json
{
  "username": "alex_martinez",
  "firstName": "Alex",
  "lastName": "Martinez",
  "email": "<EMAIL>",
  "password": "SteriBot2025!",
  "role": "user",
  "language": "en",
  "jobTitleId": "REPLACE_WITH_ACTUAL_JOB_TITLE_ID",
  "departmentId": "REPLACE_WITH_ACTUAL_DEPARTMENT_ID",
  "picture": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
}
```

Replace the jobTitleId and departmentId with actual IDs from your database before testing.
