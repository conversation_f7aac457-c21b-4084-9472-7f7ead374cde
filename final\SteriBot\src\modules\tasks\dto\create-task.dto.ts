import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsDateString, <PERSON><PERSON><PERSON><PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export enum TaskType {
  MANUAL_DISINFECTION = "manual_disinfection",
  SCHEDULED_DISINFECTION = "scheduled_disinfection",
  NAVIGATION = "navigation",
  MAINTENANCE = "maintenance",
}

export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

export class CreateTaskDto {
  @ApiProperty({
    description: 'Name of the task',
    example: 'Clean Room A'
  })
  @IsString()
  taskName: string

  @ApiProperty({
    enum: TaskType,
    description: 'Type of task to perform',
    example: TaskType.MANUAL_DISINFECTION
  })
  @IsEnum(TaskType)
  taskType: TaskType

  @ApiProperty({
    enum: TaskPriority,
    description: 'Priority level of the task',
    example: TaskPriority.HIGH
  })
  @IsEnum(TaskPriority)
  priority: TaskPriority

  @ApiProperty({
    required: false,
    description: 'Scheduled time for the task',
    example: '2024-01-01T10:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  scheduledTime?: string

  @ApiProperty({
    description: 'ID of the user who created the task',
    example: 'user123'
  })
  @IsString()
  createdBy: string

  @ApiProperty({
    description: 'ID of the robot assigned to the task',
    example: 'robot123'
  })
  @IsString()
  robotId: string

  @ApiProperty({
    description: 'ID of the zone where the task will be performed',
    example: 'zone123'
  })
  @IsString()
  zoneId: string

  @ApiProperty({
    required: false,
    description: 'Estimated duration in minutes',
    example: 30
  })
  @IsOptional()
  @IsNumber()
  estimatedDuration?: number
}
