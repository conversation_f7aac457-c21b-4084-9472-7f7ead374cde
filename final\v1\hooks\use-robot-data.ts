// hooks/use-robot-data.ts
import { useState, useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

export interface RobotPosition {
  x: number;
  y: number;
  z?: number;
  orientation?: {
    x: number;
    y: number;
    z: number;
    w: number;
  };
}

export interface BatteryData {
  percentage?: number;
  voltage?: number;
  current?: number;
  temperature?: number;
  status?: string;
}

export interface RobotData {
  position: RobotPosition;
  battery?: BatteryData;
  speed?: number;
  status?: string;
  timestamp?: string;
}

export interface MapData {
  width: number;
  height: number;
  resolution: number;
  data: number[];
  origin: {
    x: number;
    y: number;
  };
  timestamp?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  error?: string;
  lastUpdate?: string;
}

const SOCKET_URL = 'http://localhost:3001/realtime-robot-data';
const BACKEND_URL = 'http://localhost:3001/api/v1';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('customToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

export function useRobotData() {
  const [robotData, setRobotData] = useState<RobotData | null>(null);
  const [mapData, setMapData] = useState<MapData | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    connected: false
  });
  const [isStreaming, setIsStreaming] = useState(false);

  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const sessionIdRef = useRef<string | null>(null);

  // Auto-start stream when hook is first used
  useEffect(() => {
    console.log('🚀 useRobotData hook mounted, auto-starting stream...');

    // Small delay to ensure component is mounted
    const timer = setTimeout(() => {
      startStream().then((success) => {
        console.log('🚀 Auto-start stream result:', success);
      });
    }, 1000);

    return () => clearTimeout(timer);
  }, []); // Empty dependency array means this runs once on mount



  // Start real-time stream
  const startStream = async () => {
    try {
      setIsStreaming(true);
      connectSocket();

      // Wait for connection then start stream
      return new Promise<boolean>((resolve) => {
        let resolved = false;

        const checkConnection = () => {
          if (socketRef.current?.connected && !resolved) {
            // Listen for stream_started response
            socketRef.current.once('stream_started', (response) => {
              console.log('📡 Stream started response:', response);
              if (response.success && !resolved) {
                resolved = true;
                sessionIdRef.current = response.session_id;
                console.log('✅ Stream session ID:', response.session_id);
                resolve(true);
              } else if (!resolved) {
                resolved = true;
                console.log('❌ Stream start failed:', response);
                resolve(false);
              }
            });

            // Start the Socket.IO stream
            socketRef.current.emit('start_stream', {
              ip_address: '*************',
              port: 9090,
              include_robot_data: true,
              include_map_data: true,
              update_frequency: 2
            });
          } else {
            // Wait a bit and check again
            setTimeout(checkConnection, 500);
          }
        };

        checkConnection();

        // Timeout after 15 seconds
        setTimeout(() => {
          if (!resolved) {
            resolved = true;
            resolve(false);
          }
        }, 15000);
      });
    } catch (error) {
      console.error('Error starting stream:', error);
      return false;
    }
  };

  // Stop real-time stream
  const stopStream = async () => {
    try {
      if (socketRef.current?.connected && sessionIdRef.current) {
        socketRef.current.emit('stop_stream', {
          session_id: sessionIdRef.current
        });
      }

      setIsStreaming(false);
      sessionIdRef.current = null;

      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    } catch (error) {
      console.error('Error stopping stream:', error);
    }
  };

  // Connect to Socket.IO
  const connectSocket = () => {
    if (socketRef.current?.connected) {
      return;
    }

    try {
      // Add auth token to Socket.IO connection
      const token = localStorage.getItem('customToken');

      socketRef.current = io(SOCKET_URL, {
        auth: {
          token: token
        },
        query: {
          token: token
        }
      });

      socketRef.current.on('connect', () => {
        console.log('🔌 Socket.IO connected!');
        setConnectionStatus({
          connected: true,
          lastUpdate: new Date().toISOString()
        });
      });

      // Listen for robot data
      socketRef.current.on('robot_data', (message) => {
        try {
          console.log('🤖 Received robot_data:', message);
          if (message) {
            const position = message.position || { x: 0, y: 0 };
            const battery = message.battery_level;

            console.log('📍 Position data:', position);

            const newRobotData: RobotData = {
              position: {
                x: typeof position.x === 'number' ? position.x : 0,
                y: typeof position.y === 'number' ? position.y : 0,
                z: typeof position.z === 'number' ? position.z : 0,
                orientation: position.orientation
              },
              battery: battery && typeof battery.percentage === 'number' ? {
                percentage: battery.percentage,
                voltage: battery.voltage,
                current: battery.current,
                status: battery.status
              } : undefined,
              speed: undefined,
              status: 'active',
              timestamp: message.timestamp || new Date().toISOString()
            };

            console.log('📊 Setting robot data:', newRobotData);
            setRobotData(newRobotData);

            // Update connection status since we're receiving data
            setConnectionStatus({
              connected: true,
              lastUpdate: new Date().toISOString()
            });
          }
        } catch (error) {
          console.error('Error processing robot data:', error);
        }
      });

      // Listen for map data
      socketRef.current.on('map_data', (message) => {
        try {
          if (message && message.info) {
            setMapData({
              width: message.info.width || 0,
              height: message.info.height || 0,
              resolution: message.info.resolution || 0.05,
              data: message.data || [],
              origin: message.info.origin || { x: 0, y: 0 },
              timestamp: message.timestamp || new Date().toISOString()
            });
          }
        } catch (error) {
          console.error('Error processing map data:', error);
        }
      });

      // Listen for connection events
      socketRef.current.on('connection', (message) => {
        try {
          if (message.event === 'connected') {
            setConnectionStatus({
              connected: true,
              lastUpdate: new Date().toISOString()
            });
          } else if (message.status) {
            setConnectionStatus({
              connected: message.status === 'connected',
              error: message.error_message,
              lastUpdate: new Date().toISOString()
            });
          }
        } catch (error) {
          console.error('Error processing connection event:', error);
        }
      });

      socketRef.current.on('disconnect', () => {
        console.log('Socket.IO disconnected');
        setConnectionStatus({
          connected: false,
          lastUpdate: new Date().toISOString()
        });

        // Auto-reconnect if streaming is active
        if (isStreaming) {
          reconnectTimeoutRef.current = setTimeout(() => {
            connectSocket();
          }, 3000);
        }
      });

      socketRef.current.on('connect_error', (error) => {
        console.error('Socket.IO connection error:', error);
        setConnectionStatus({
          connected: false,
          error: 'Socket.IO connection error',
          lastUpdate: new Date().toISOString()
        });
      });

      // Listen for stream events (handled in startStream function)

      socketRef.current.on('stream_error', (message) => {
        console.error('Stream error:', message);
        setConnectionStatus({
          connected: false,
          error: message.error,
          lastUpdate: new Date().toISOString()
        });
      });

    } catch (error) {
      console.error('Error creating Socket.IO connection:', error);
      setConnectionStatus({
        connected: false,
        error: 'Failed to create Socket.IO connection',
        lastUpdate: new Date().toISOString()
      });
    }
  };

  // Save map function
  const saveMap = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/robots/map/save`, {
        method: 'POST',
        headers: getAuthHeaders()
      });

      if (response.status === 401) {
        throw new Error('Authentication failed - please check your login token');
      }

      const result = await response.json();
      if (result.success) {
        // Download file with proper authentication
        const token = localStorage.getItem('customToken');
        const downloadUrl = `http://localhost:3001${result.download_url}`;

        try {
          // Fetch the file with authentication headers
          const fileResponse = await fetch(downloadUrl, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (fileResponse.ok) {
            // Create blob and download
            const blob = await fileResponse.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = result.filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
          } else {
            throw new Error(`Download failed: ${fileResponse.status} ${fileResponse.statusText}`);
          }
        } catch (downloadError) {
          console.error('Download error:', downloadError);
          throw new Error(`Failed to download file: ${downloadError instanceof Error ? downloadError.message : 'Unknown error'}`);
        }

        return {
          success: true,
          filename: result.filename,
          fileSize: result.file_size
        };
      } else {
        throw new Error(result.error || 'Failed to save map');
      }
    } catch (error) {
      console.error('Error saving map:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  // Test ROS2 connection
  const testConnection = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/robots/test/connection`, {
        method: 'POST',
        headers: getAuthHeaders()
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error testing connection:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
      if (sessionIdRef.current) {
        stopStream();
      }
    };
  }, []);

  return {
    robotData,
    mapData,
    connectionStatus,
    isStreaming,
    startStream,
    stopStream,
    saveMap,
    testConnection
  };
}
