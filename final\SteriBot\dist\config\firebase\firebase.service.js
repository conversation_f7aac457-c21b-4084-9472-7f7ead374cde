"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirebaseService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const admin = require("firebase-admin");
const firestore_1 = require("firebase-admin/firestore");
const auth_1 = require("firebase-admin/auth");
const storage_1 = require("firebase-admin/storage");
let FirebaseService = class FirebaseService {
    constructor(configService) {
        this.configService = configService;
    }
    async onModuleInit() {
        try {
            if (process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {
                process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
            }
            const serviceAccount = {
                type: "service_account",
                project_id: this.configService.get("FIREBASE_PROJECT_ID"),
                private_key_id: this.configService.get("FIREBASE_PRIVATE_KEY_ID"),
                private_key: this.configService.get("FIREBASE_PRIVATE_KEY")?.replace(/\\n/g, "\n"),
                client_email: this.configService.get("FIREBASE_CLIENT_EMAIL"),
                client_id: this.configService.get("FIREBASE_CLIENT_ID"),
                auth_uri: "https://accounts.google.com/o/oauth2/auth",
                token_uri: "https://oauth2.googleapis.com/token",
                auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
                client_x509_cert_url: this.configService.get("FIREBASE_CLIENT_X509_CERT_URL"),
            };
            if (!admin.apps.length) {
                admin.initializeApp({
                    credential: admin.credential.cert(serviceAccount),
                    databaseURL: this.configService.get("FIREBASE_DATABASE_URL"),
                    storageBucket: this.configService.get("FIREBASE_STORAGE_BUCKET"),
                });
            }
            this.firestore = (0, firestore_1.getFirestore)();
            this.auth = (0, auth_1.getAuth)();
            this.storage = (0, storage_1.getStorage)();
            console.log('Firebase initialized successfully');
        }
        catch (error) {
            console.error('Firebase initialization error:', error);
            throw error;
        }
    }
    getFirestore() {
        return this.firestore;
    }
    getAuth() {
        return this.auth;
    }
    getStorage() {
        return this.storage;
    }
    async uploadFile(buffer, fileName, contentType, folder = 'profile-pictures') {
        try {
            const bucketName = this.configService.get("FIREBASE_STORAGE_BUCKET");
            if (!bucketName) {
                throw new Error('Firebase Storage bucket name not configured. Please set FIREBASE_STORAGE_BUCKET in environment variables.');
            }
            const bucket = this.storage.bucket(bucketName);
            const filePath = `${folder}/${fileName}`;
            const file = bucket.file(filePath);
            await file.save(buffer, {
                metadata: {
                    contentType,
                },
                public: true,
            });
            const [url] = await file.getSignedUrl({
                action: 'read',
                expires: '03-09-2491',
            });
            return url;
        }
        catch (error) {
            console.error('Error uploading file to Firebase Storage:', error);
            throw new Error('Failed to upload file');
        }
    }
    async deleteFile(filePath) {
        try {
            const bucketName = this.configService.get("FIREBASE_STORAGE_BUCKET");
            if (!bucketName) {
                throw new Error('Firebase Storage bucket name not configured. Please set FIREBASE_STORAGE_BUCKET in environment variables.');
            }
            const bucket = this.storage.bucket(bucketName);
            const file = bucket.file(filePath);
            await file.delete();
        }
        catch (error) {
            console.error('Error deleting file from Firebase Storage:', error);
            throw new Error('Failed to delete file');
        }
    }
    extractFilePathFromUrl(url) {
        try {
            const urlParts = url.split('/');
            const bucketIndex = urlParts.findIndex(part => part.includes('appspot.com'));
            if (bucketIndex !== -1 && bucketIndex + 2 < urlParts.length) {
                return urlParts.slice(bucketIndex + 2).join('/').split('?')[0];
            }
            throw new Error('Invalid Firebase Storage URL');
        }
        catch (error) {
            console.error('Error extracting file path from URL:', error);
            throw new Error('Invalid Firebase Storage URL');
        }
    }
};
exports.FirebaseService = FirebaseService;
exports.FirebaseService = FirebaseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], FirebaseService);
//# sourceMappingURL=firebase.service.js.map