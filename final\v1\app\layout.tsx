import type { Metada<PERSON> } from 'next'
import './globals.css'
import { ReduxProvider } from '../store/provider'

export const metadata: Metadata = {
  title: 'Steribot',
  description: 'Steribot',
  generator: 'Steribot',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <ReduxProvider>
          {children}
        </ReduxProvider>
      </body>
    </html>
  )
}
