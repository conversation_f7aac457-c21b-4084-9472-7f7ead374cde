/**
 * Redux Store Configuration
 */

import { configureStore } from '@reduxjs/toolkit';
import { profileSlice } from '@/store/slices/profileSlice';
import { authSlice } from '@/store/slices/authSlice';
import { uiSlice } from '@/store/slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice.reducer,
    profile: profileSlice.reducer,
    ui: uiSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'ui/addToast',
          'ui/openModal',
        ],
        ignoredPaths: ['ui.toasts', 'ui.modals'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Infer the type of makeStore
export type AppStore = typeof store;

// Export store instance
export default store;
