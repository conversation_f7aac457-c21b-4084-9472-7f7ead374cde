#!/usr/bin/env node
/**
 * Simple API endpoint test for SteriBot
 * Tests the key endpoints without complex dependencies
 */

const http = require('http');
const WebSocket = require('ws');

const BACKEND_HOST = 'localhost';
const BACKEND_PORT = 3001;
const ROS2_IP = '*************';
const ROS2_PORT = 9090;

console.log('🤖 SteriBot API Endpoint Test');
console.log('=' .repeat(50));
console.log(`Backend: http://${BACKEND_HOST}:${BACKEND_PORT}`);
console.log(`ROS2 Target: ${ROS2_IP}:${ROS2_PORT}`);
console.log('');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: BACKEND_HOST,
      port: BACKEND_PORT,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testHealthEndpoint() {
  console.log('🏥 Testing backend health...');
  try {
    const result = await makeRequest('/api/v1/robots/data/health');
    if (result.status === 200) {
      console.log(`   ✅ Backend health: ${result.data.status}`);
      return true;
    } else {
      console.log(`   ❌ Backend health failed: ${result.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Backend health error: ${error.message}`);
    return false;
  }
}

async function testROS2Connection() {
  console.log('🔗 Testing ROS2 connection...');
  try {
    const result = await makeRequest('/api/v1/robots/test/connection', 'POST');
    if (result.status === 201 && result.data.success) {
      console.log(`   ✅ ROS2 connection: ${result.data.connection_test.status}`);
      console.log(`   📍 Position data: ${result.data.connection_test.has_position ? 'Available' : 'Not available'}`);
      console.log(`   🔋 Battery data: ${result.data.connection_test.has_battery ? 'Available' : 'Not available'}`);
      return result.data;
    } else {
      console.log(`   ❌ ROS2 connection failed: ${result.status}`);
      console.log(`   Error: ${JSON.stringify(result.data)}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ ROS2 connection error: ${error.message}`);
    return false;
  }
}

async function testRealtimeStream() {
  console.log('📡 Testing real-time stream start...');
  try {
    const streamConfig = {
      ip_address: ROS2_IP,
      port: ROS2_PORT,
      include_robot_data: true,
      include_map_data: true,
      update_frequency: 2
    };

    const result = await makeRequest('/api/v1/robots/realtime/start', 'POST', streamConfig);
    if (result.status === 201 && result.data.success) {
      console.log(`   ✅ Stream started: ${result.data.session_id}`);
      return result.data;
    } else {
      console.log(`   ❌ Stream start failed: ${result.status}`);
      console.log(`   Error: ${JSON.stringify(result.data)}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Stream start error: ${error.message}`);
    return false;
  }
}

async function testWebSocket() {
  console.log('🔌 Testing WebSocket connection...');
  
  return new Promise((resolve) => {
    try {
      const ws = new WebSocket(`ws://${BACKEND_HOST}:${BACKEND_PORT}/realtime-robot-data`);
      let connected = false;
      let messagesReceived = 0;
      
      const timeout = setTimeout(() => {
        if (!connected) {
          ws.close();
          console.log('   ❌ WebSocket connection timeout');
          resolve(false);
        } else {
          ws.close();
          console.log(`   ✅ WebSocket test completed: ${messagesReceived} messages received`);
          resolve(messagesReceived > 0);
        }
      }, 10000);

      ws.on('open', () => {
        connected = true;
        console.log('   ✅ WebSocket connected');
      });

      ws.on('message', (data) => {
        messagesReceived++;
        if (messagesReceived <= 3) {
          try {
            const message = JSON.parse(data.toString());
            console.log(`   📨 Message ${messagesReceived}: ${message.event || 'unknown'}`);
          } catch (e) {
            console.log(`   📨 Message ${messagesReceived}: [binary data]`);
          }
        }
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        console.log(`   ❌ WebSocket error: ${error.message}`);
        resolve(false);
      });

      ws.on('close', () => {
        clearTimeout(timeout);
      });

    } catch (error) {
      console.log(`   ❌ WebSocket setup error: ${error.message}`);
      resolve(false);
    }
  });
}

async function testMapExport() {
  console.log('🗺️  Testing map export...');
  try {
    const exportConfig = {
      ip_address: ROS2_IP,
      port: ROS2_PORT,
      format: 'png',
      color_scheme: 'colored',
      filename: 'test_map'
    };

    const result = await makeRequest('/api/v1/robots/map/export', 'POST', exportConfig);
    if (result.status === 201 && result.data.success) {
      console.log(`   ✅ Map exported: ${result.data.filename}`);
      console.log(`   📁 File size: ${(result.data.file_size / 1024).toFixed(1)} KB`);
      return result.data;
    } else {
      console.log(`   ❌ Map export failed: ${result.status}`);
      console.log(`   Error: ${JSON.stringify(result.data)}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Map export error: ${error.message}`);
    return false;
  }
}

async function cleanup(sessionId) {
  if (sessionId) {
    console.log('🧹 Cleaning up stream...');
    try {
      await makeRequest('/api/v1/robots/realtime/stop', 'POST', { session_id: sessionId });
      console.log('   ✅ Stream stopped');
    } catch (error) {
      console.log(`   ⚠️  Cleanup error: ${error.message}`);
    }
  }
}

async function main() {
  const results = {
    health: false,
    ros2: false,
    stream: false,
    websocket: false,
    mapExport: false
  };

  let sessionId = null;

  try {
    results.health = await testHealthEndpoint();
    results.ros2 = await testROS2Connection();
    
    const streamResult = await testRealtimeStream();
    if (streamResult) {
      results.stream = true;
      sessionId = streamResult.session_id;
    }
    
    results.websocket = await testWebSocket();
    results.mapExport = await testMapExport();

  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
  } finally {
    await cleanup(sessionId);
  }

  // Print summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(50));
  
  const tests = [
    { name: 'Backend Health', status: results.health },
    { name: 'ROS2 Connection', status: results.ros2 },
    { name: 'Real-time Stream', status: results.stream },
    { name: 'WebSocket Data', status: results.websocket },
    { name: 'Map Export', status: results.mapExport }
  ];
  
  tests.forEach(test => {
    console.log(`${test.status ? '✅' : '❌'} ${test.name}`);
  });
  
  const allPassed = tests.every(test => test.status);
  console.log(`\n🎯 Overall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🚀 Ready for Frontend Integration!');
    console.log('WebSocket: ws://localhost:3001/realtime-robot-data');
    console.log('API Docs: http://localhost:3001/api/docs');
  } else {
    console.log('\n🔧 Check ROS2 system at *************:9090');
  }
}

if (require.main === module) {
  main();
}
