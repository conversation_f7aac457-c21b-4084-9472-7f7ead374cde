/**
 * Authentication Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { authAPI, LoginCredentials, RegisterData } from '@/api';

export interface AuthUser {
  id: string;
  username: string;
  email: string;
  role: string;
}

export interface UseAuthReturn {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshToken: () => Promise<boolean>;
  verifyToken: () => Promise<boolean>;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = !!user && authAPI.isAuthenticated();

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Verify token on mount
  const verifyToken = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await authAPI.verifyToken();
      
      if (response.success && response.data?.valid) {
        setUser(response.data.user);
        return true;
      } else {
        setUser(null);
        return false;
      }
    } catch (err) {
      setUser(null);
      setError(err instanceof Error ? err.message : 'Token verification failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Login function
  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await authAPI.login(credentials);
      
      if (response.success && response.data?.user) {
        setUser(response.data.user);
        return true;
      } else {
        setError(response.error || 'Login failed');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Register function
  const register = useCallback(async (userData: RegisterData): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await authAPI.register(userData);
      
      if (response.success && response.data?.user) {
        setUser({
          id: response.data.user.id,
          username: response.data.user.username,
          email: response.data.user.email,
          role: 'user', // Default role
        });
        return true;
      } else {
        setError(response.error || 'Registration failed');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Logout function
  const logout = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authAPI.logout();
      setUser(null);
      setError(null);
    } catch (err) {
      // Even if logout fails on server, clear local state
      setUser(null);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Refresh token function
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const response = await authAPI.refreshToken();
      
      if (response.success) {
        // Verify the new token
        return await verifyToken();
      } else {
        setUser(null);
        setError(response.error || 'Token refresh failed');
        return false;
      }
    } catch (err) {
      setUser(null);
      setError(err instanceof Error ? err.message : 'Token refresh failed');
      return false;
    }
  }, [verifyToken]);

  // Initialize auth state on mount
  useEffect(() => {
    if (authAPI.isAuthenticated()) {
      verifyToken();
    } else {
      setIsLoading(false);
    }
  }, [verifyToken]);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
    refreshToken,
    verifyToken,
  };
};
