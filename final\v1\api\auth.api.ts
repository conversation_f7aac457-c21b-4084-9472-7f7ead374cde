/**
 * Authentication API - Handles user authentication
 */

import { BaseAPI, ApiResponse } from './base';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  customToken?: string;
  authToken?: string;
  userId?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    role: string;
  };
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface RegisterResponse {
  success: boolean;
  user?: {
    id: string;
    username: string;
    email: string;
  };
  token?: string;
}

class AuthAPI extends BaseAPI {
  /**
   * Login user
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await this.post<LoginResponse>('/api/v1/auth/login', credentials);
      
      if (response.success && response.data) {
        // Store tokens in localStorage
        const { token, customToken, authToken, userId } = response.data;
        
        if (token) localStorage.setItem('authToken', token);
        if (customToken) localStorage.setItem('customToken', customToken);
        if (authToken) localStorage.setItem('authToken', authToken);
        if (userId) localStorage.setItem('userId', userId);
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterData): Promise<ApiResponse<RegisterResponse>> {
    try {
      const response = await this.post<RegisterResponse>('/api/v1/auth/register', userData);
      
      if (response.success && response.data?.token) {
        // Store token in localStorage
        localStorage.setItem('authToken', response.data.token);
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      };
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<ApiResponse<{ message: string }>> {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('customToken');
      
      if (token) {
        await this.request('/api/v1/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
      
      // Clear all auth data from localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('customToken');
      localStorage.removeItem('userId');
      
      return {
        success: true,
        data: { message: 'Logged out successfully' },
      };
    } catch (error) {
      // Even if the API call fails, clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('customToken');
      localStorage.removeItem('userId');
      
      return {
        success: true,
        data: { message: 'Logged out locally' },
      };
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (!refreshToken) {
        return {
          success: false,
          error: 'No refresh token available',
        };
      }
      
      const response = await this.post<{ token: string }>('/api/v1/auth/refresh', {
        refreshToken,
      });
      
      if (response.success && response.data?.token) {
        localStorage.setItem('authToken', response.data.token);
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Token refresh failed',
      };
    }
  }

  /**
   * Verify current token
   */
  async verifyToken(): Promise<ApiResponse<{ valid: boolean; user?: any }>> {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('customToken');
      
      if (!token) {
        return {
          success: false,
          error: 'No token found',
        };
      }
      
      const response = await this.request<{ valid: boolean; user?: any }>('/api/v1/auth/verify', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Token verification failed',
      };
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await this.post<{ message: string }>('/api/v1/auth/forgot-password', {
        email,
      });
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Password reset request failed',
      };
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await this.post<{ message: string }>('/api/v1/auth/reset-password', {
        token,
        password: newPassword,
      });
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Password reset failed',
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('authToken') || localStorage.getItem('customToken');
    return !!token;
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return localStorage.getItem('authToken') || localStorage.getItem('customToken');
  }

  /**
   * Get current user ID
   */
  getCurrentUserId(): string | null {
    return localStorage.getItem('userId');
  }
}

// Export singleton instance
export const authAPI = new AuthAPI();
export default authAPI;
