/**
 * Authentication Redux Slice
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authAPI, LoginCredentials, RegisterData } from '@/api';

// Async thunks for API calls
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(credentials);
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Login failed');
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async (userData: RegisterData, { rejectWithValue }) => {
    try {
      const response = await authAPI.register(userData);
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Registration failed');
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Registration failed');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.logout();
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Logout failed');
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Logout failed');
    }
  }
);

export const verifyToken = createAsyncThunk(
  'auth/verifyToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.verifyToken();
      if (response.success && response.data?.valid) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Token verification failed');
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Token verification failed');
    }
  }
);

// Auth state interface
export interface AuthState {
  // User data
  user: {
    id: string;
    username: string;
    email: string;
    role: string;
  } | null;
  
  // Authentication status
  isAuthenticated: boolean;
  token: string | null;
  
  // Loading states
  isLoading: boolean;
  isLoggingIn: boolean;
  isRegistering: boolean;
  isLoggingOut: boolean;
  isVerifying: boolean;
  
  // Error states
  error: string | null;
  loginError: string | null;
  registerError: string | null;
  
  // UI states
  lastLoginTime: number | null;
  sessionExpiry: number | null;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  token: null,
  isLoading: false,
  isLoggingIn: false,
  isRegistering: false,
  isLoggingOut: false,
  isVerifying: false,
  error: null,
  loginError: null,
  registerError: null,
  lastLoginTime: null,
  sessionExpiry: null,
};

// Auth slice
export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Clear errors
    clearErrors: (state) => {
      state.error = null;
      state.loginError = null;
      state.registerError = null;
    },
    
    // Clear login error specifically
    clearLoginError: (state) => {
      state.loginError = null;
    },
    
    // Clear register error specifically
    clearRegisterError: (state) => {
      state.registerError = null;
    },
    
    // Reset auth state
    resetAuth: (state) => {
      return initialState;
    },
    
    // Set session expiry
    setSessionExpiry: (state, action: PayloadAction<number>) => {
      state.sessionExpiry = action.payload;
    },
    
    // Update user data
    updateUser: (state, action: PayloadAction<Partial<AuthState['user']>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    // Login user
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoggingIn = true;
        state.loginError = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoggingIn = false;
        state.isAuthenticated = true;
        state.user = action.payload.user || null;
        state.token = action.payload.token || action.payload.customToken || action.payload.authToken || null;
        state.lastLoginTime = Date.now();
        state.loginError = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoggingIn = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.loginError = action.payload as string;
      });

    // Register user
    builder
      .addCase(registerUser.pending, (state) => {
        state.isRegistering = true;
        state.registerError = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isRegistering = false;
        state.isAuthenticated = true;
        state.user = action.payload.user ? {
          id: action.payload.user.id,
          username: action.payload.user.username,
          email: action.payload.user.email,
          role: 'user',
        } : null;
        state.token = action.payload.token || null;
        state.lastLoginTime = Date.now();
        state.registerError = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isRegistering = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.registerError = action.payload as string;
      });

    // Logout user
    builder
      .addCase(logoutUser.pending, (state) => {
        state.isLoggingOut = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoggingOut = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.lastLoginTime = null;
        state.sessionExpiry = null;
        state.error = null;
        state.loginError = null;
        state.registerError = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoggingOut = false;
        // Even if logout fails on server, clear local state
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.lastLoginTime = null;
        state.sessionExpiry = null;
      });

    // Verify token
    builder
      .addCase(verifyToken.pending, (state) => {
        state.isVerifying = true;
      })
      .addCase(verifyToken.fulfilled, (state, action) => {
        state.isVerifying = false;
        state.isAuthenticated = true;
        state.user = action.payload.user || state.user;
        state.error = null;
      })
      .addCase(verifyToken.rejected, (state, action) => {
        state.isVerifying = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  clearErrors,
  clearLoginError,
  clearRegisterError,
  resetAuth,
  setSessionExpiry,
  updateUser,
} = authSlice.actions;

// Export selectors
export const selectAuth = (state: any) => state.auth;
export const selectUser = (state: any) => state.auth.user;
export const selectIsAuthenticated = (state: any) => state.auth.isAuthenticated;
export const selectIsLoggingIn = (state: any) => state.auth.isLoggingIn;
export const selectIsRegistering = (state: any) => state.auth.isRegistering;
export const selectIsLoggingOut = (state: any) => state.auth.isLoggingOut;
export const selectIsVerifying = (state: any) => state.auth.isVerifying;
export const selectAuthError = (state: any) => state.auth.error;
export const selectLoginError = (state: any) => state.auth.loginError;
export const selectRegisterError = (state: any) => state.auth.registerError;
export const selectLastLoginTime = (state: any) => state.auth.lastLoginTime;
export const selectSessionExpiry = (state: any) => state.auth.sessionExpiry;

// Export reducer
export default authSlice.reducer;
