"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJobTitleDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateJobTitleDto {
}
exports.CreateJobTitleDto = CreateJobTitleDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job title name',
        example: 'Software Engineer',
        minLength: 2
    }),
    (0, class_validator_1.IsString)({ message: 'Job title name must be a string' }),
    (0, class_validator_1.MinLength)(2, { message: 'Job title name must be at least 2 characters long' }),
    __metadata("design:type", String)
], CreateJobTitleDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job title description',
        example: 'Responsible for developing and maintaining software applications',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Description must be a string' }),
    __metadata("design:type", String)
], CreateJobTitleDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Department ID that this job title belongs to',
        example: 'dept_123'
    }),
    (0, class_validator_1.IsString)({ message: 'Department ID must be a string' }),
    __metadata("design:type", String)
], CreateJobTitleDto.prototype, "departmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job level or seniority',
        example: 'Senior',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Job level must be a string' }),
    __metadata("design:type", String)
], CreateJobTitleDto.prototype, "level", void 0);
//# sourceMappingURL=create-job-title.dto.js.map