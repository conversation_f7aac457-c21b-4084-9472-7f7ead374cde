import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateRoomDto } from "./dto/create-room.dto";
import type { UpdateRoomDto } from "./dto/update-room.dto";
export declare class RoomsService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createRoomDto: CreateRoomDto): Promise<{
        roomId: string;
        createdAt: Date;
        roomNumber: string;
        roomName: string;
        floorId: string;
        roomType: string;
        area: number;
        capacity: number;
        requiresDisinfection?: boolean;
        lastDisinfected?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findByFloor(floorId: string): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateRoomDto: UpdateRoomDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
