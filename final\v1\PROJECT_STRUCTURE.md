# Project Structure Documentation

Ce document décrit la nouvelle structure organisée du projet v1 selon les notes manuscrites.

## 📁 Structure des Dossiers

### `/api` - Dossier API
Contient toutes les classes et fonctions pour les appels API :
- `index.ts` - Configuration de base et classe BaseAPI
- `auth.api.ts` - API d'authentification
- `profile.api.ts` - API de profil utilisateur
- `upload.api.ts` - API d'upload de fichiers

### `/hooks` - Hooks Personnalisés
Contient tous les hooks React personnalisés :
- `index.ts` - Export de tous les hooks
- `use-auth.ts` - Hook d'authentification
- `use-profile.ts` - Hook de profil utilisateur
- `use-upload.ts` - Hook d'upload de fichiers
- `use-mobile.tsx` - Hook pour la détection mobile
- `use-toast.ts` - Hook pour les notifications
- `use-robot-data.ts` - Hook pour les données des robots

### `/store` - Redux Store
Structure Redux organisée :
- `index.ts` - Configuration du store principal
- `hooks.ts` - Hooks Redux typés
- `provider.tsx` - Provider Redux
- `/slices` - Slices Redux
  - `authSlice.ts` - Gestion de l'authentification
  - `profileSlice.ts` - Gestion du profil utilisateur
  - `uiSlice.ts` - Gestion de l'interface utilisateur
  - `index.ts` - Export de tous les slices

### `/screens` - Écrans/Pages
Contient les composants d'écran principaux :
- `index.ts` - Export de tous les screens
- `LoginScreen.tsx` - Écran de connexion
- `DashboardScreen.tsx` - Écran du tableau de bord
- `ProfileScreen.tsx` - Écran de profil utilisateur

### `/styles` - Styles CSS
Structure CSS organisée :
- `globals.css` - Fichier principal qui importe tous les autres
- `base.css` - Styles de base et variables CSS
- `components.css` - Styles des composants
- `animations.css` - Animations et keyframes
- `index.css` - Fichier d'export alternatif

## 🔧 Utilisation

### APIs
```typescript
import { authAPI, profileAPI, uploadAPI } from '@/api';

// Utilisation
const loginResult = await authAPI.login({ username, password });
const profile = await profileAPI.fetchProfile();
const uploadResult = await uploadAPI.uploadProfilePhoto(file);
```

### Hooks
```typescript
import { useAuth, useProfile, useUpload } from '@/hooks';

// Dans un composant
const { user, login, logout } = useAuth();
const { profile, updateProfile } = useProfile();
const { uploadProfilePhoto } = useUpload();
```

### Redux Store
```typescript
import { useDispatch, useSelector } from 'react-redux';
import { loginUser, selectUser, addToast } from '@/store/slices';

// Dans un composant
const dispatch = useDispatch();
const user = useSelector(selectUser);
dispatch(loginUser({ username, password }));
```

### Screens
```typescript
import { LoginScreen, DashboardScreen, ProfileScreen } from '@/screens';

// Utilisation dans les pages Next.js
export default function LoginPage() {
  return <LoginScreen />;
}
```

### Styles
```css
/* Dans globals.css - tous les styles sont automatiquement importés */
@import './base.css';
@import './components.css';
@import './animations.css';
```

## 📋 Avantages de cette Structure

1. **Séparation des Responsabilités** : Chaque dossier a une responsabilité claire
2. **Réutilisabilité** : Les APIs, hooks et composants sont facilement réutilisables
3. **Maintenabilité** : Code organisé et facile à maintenir
4. **Scalabilité** : Structure qui peut grandir avec le projet
5. **TypeScript** : Typage complet pour une meilleure DX
6. **Performance** : Imports optimisés et code splitting

## 🚀 Prochaines Étapes

1. Migrer les composants existants pour utiliser les nouveaux hooks et APIs
2. Ajouter des tests pour chaque module
3. Documenter les APIs et hooks individuellement
4. Optimiser les performances avec React.memo et useMemo
5. Ajouter la gestion d'erreurs globale

## 📝 Notes

Cette structure suit les meilleures pratiques React/Next.js et Redux Toolkit, en gardant le code organisé et maintenable selon les notes manuscrites fournies.
