import { Injectable, type CanActivate, type ExecutionContext, UnauthorizedException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"

@Injectable()
export class FirebaseAuthGuard implements CanActivate {
  constructor(private firebaseService: FirebaseService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const token = this.extractTokenFromHeader(request)

    if (!token) {
      throw new UnauthorizedException("No token provided")
    }

    try {
      // First try to verify as an ID token
      let decodedToken
      try {
        decodedToken = await this.firebaseService.getAuth().verifyIdToken(token)
      } catch (idTokenError) {
        // If ID token verification fails, try to decode as custom token
        // Custom tokens are JWTs that we can decode to get the user info
        try {
          const jwt = require('jsonwebtoken')
          const decoded = jwt.decode(token)

          if (decoded && decoded.uid) {
            // Create a mock decoded token structure for custom tokens
            decodedToken = {
              uid: decoded.uid,
              ...decoded.claims,
              // Add any custom claims that were set during token creation
            }
          } else {
            throw new Error('Invalid token structure')
          }
        } catch (customTokenError) {
          console.error('Token verification failed:', { idTokenError: idTokenError.message, customTokenError: customTokenError.message })
          throw new UnauthorizedException("Invalid token")
        }
      }

      request.user = decodedToken
      return true
    } catch (error) {
      console.error('Auth guard error:', error)
      throw new UnauthorizedException("Invalid token")
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(" ") ?? []
    return type === "Bearer" ? token : undefined
  }
}
