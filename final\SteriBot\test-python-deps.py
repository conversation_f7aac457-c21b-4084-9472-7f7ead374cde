#!/usr/bin/env python3
"""
Test script to verify Python dependencies are installed
"""

print("🐍 Testing Python Dependencies")
print("=" * 50)

# Test required imports
try:
    import asyncio
    print("✅ asyncio - OK")
except ImportError as e:
    print(f"❌ asyncio - FAILED: {e}")

try:
    import json
    print("✅ json - OK")
except ImportError as e:
    print(f"❌ json - FAILED: {e}")

try:
    import websockets
    print("✅ websockets - OK")
    print(f"   Version: {websockets.__version__}")
except ImportError as e:
    print(f"❌ websockets - FAILED: {e}")

try:
    import logging
    print("✅ logging - OK")
except ImportError as e:
    print(f"❌ logging - FAILED: {e}")

try:
    import sys
    print("✅ sys - OK")
except ImportError as e:
    print(f"❌ sys - FAILED: {e}")

try:
    from datetime import datetime
    print("✅ datetime - OK")
except ImportError as e:
    print(f"❌ datetime - FAILED: {e}")

try:
    from typing import Dict, Optional, Any
    print("✅ typing - OK")
except ImportError as e:
    print(f"❌ typing - FAILED: {e}")

# Test optional imports
try:
    import foxglove
    print("✅ foxglove - OK (optional)")
except ImportError as e:
    print(f"⚠️  foxglove - Not available (optional): {e}")

print("\n" + "=" * 50)
print("🎯 SUMMARY")
print("=" * 50)

# Test basic WebSocket functionality
try:
    import asyncio
    import websockets
    
    async def test_websocket():
        try:
            # Try to connect to the ROS2 bridge
            uri = "ws://*************:9090"
            print(f"🔗 Testing WebSocket connection to {uri}")
            
            async with websockets.connect(uri, timeout=5) as websocket:
                print("✅ WebSocket connection successful!")
                
                # Send a simple test message
                test_msg = {
                    "op": "subscribe",
                    "topic": "/odom",
                    "type": "nav_msgs/Odometry"
                }
                await websocket.send(json.dumps(test_msg))
                print("✅ Test message sent")
                
                # Try to receive a response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3)
                    print("✅ Received response from ROS2 bridge")
                    return True
                except asyncio.TimeoutError:
                    print("⚠️  No response received (timeout)")
                    return True  # Connection worked, just no immediate response
                    
        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            return False
    
    # Run the test
    result = asyncio.run(test_websocket())
    
    if result:
        print("\n🎉 All dependencies are working!")
        print("✅ Python scripts should work with the backend")
    else:
        print("\n⚠️  Dependencies OK, but ROS2 connection failed")
        print("🔧 Check if ROS2 bridge is running at *************:9090")
        
except Exception as e:
    print(f"\n❌ Dependency test failed: {e}")

print("\n📋 Next steps:")
print("1. If dependencies are OK, test the backend API")
print("2. If ROS2 connection failed, check the bridge status")
print("3. Try the frontend real-time data integration")
