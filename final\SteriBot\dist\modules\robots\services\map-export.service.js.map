{"version": 3, "file": "map-export.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/robots/services/map-export.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,iDAAsC;AACtC,6BAA6B;AAC7B,yBAAyB;AACzB,0DAQ+B;AAGxB,IAAM,gBAAgB,wBAAtB,MAAM,gBAAgB;IAM3B;QALiB,WAAM,GAAG,IAAI,eAAM,CAAC,kBAAgB,CAAC,IAAI,CAAC,CAAC;QAG3C,qBAAgB,GAAG,GAAG,CAAC;QAGtC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAGnE,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAEO,qBAAqB;QAC3B,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACzC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA0B;QAC7C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;YAGxF,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3H,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,IAAI,aAAa,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC;YAC/F,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,4BAAW,CAAC,GAAG,CAAC;YACjD,MAAM,QAAQ,GAAG,GAAG,YAAY,GAAG,SAAS,IAAI,MAAM,EAAE,CAAC;YACzD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAG7D,MAAM,IAAI,GAAG;gBACX,IAAI,CAAC,gBAAgB;gBACrB,MAAM,EAAE,OAAO,CAAC,UAAU;gBAC1B,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE;gBAC3C,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,MAAM;gBAClB,gBAAgB,EAAE,OAAO,CAAC,YAAY,IAAI,4BAAW,CAAC,OAAO;gBAC7D,SAAS,EAAE,CAAC,OAAO,CAAC,YAAY,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE;gBACnD,WAAW,EAAE,IAAI;aAClB,CAAC;YAEF,IAAI,OAAO,CAAC,YAAY,IAAI,MAAM,KAAK,4BAAW,CAAC,IAAI,EAAE,CAAC;gBACxD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC;YAED,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;gBACnC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3B,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAG1D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAEpD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBAEnB,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAEtC,MAAM,QAAQ,GAA8B;oBAC1C,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,QAAQ;oBAClB,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC;oBACnD,SAAS,EAAE,KAAK,CAAC,IAAI;oBACrB,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBACxD,QAAQ,EAAE;wBACR,UAAU,EAAE,IAAI;wBAChB,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC;wBACpC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC;wBACtC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;qBACvB;oBACD,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACrC,YAAY,EAAE,4BAA4B,QAAQ,EAAE;iBACrD,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,QAAQ,KAAK,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC;gBAClF,OAAO,QAAQ,CAAC;YAClB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,sBAAsB,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAA0B;QAC9C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,OAAO,CAAC,YAAY,CAAC,MAAM,SAAS,CAAC,CAAC;YAEvF,MAAM,OAAO,GAA2D,EAAE,CAAC;YAC3E,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,WAAW,GAAG,CAAC,CAAC;YAGpB,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC9C,IAAI,CAAC;oBACH,MAAM,aAAa,GAAsB;wBACvC,UAAU;wBACV,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,GAAG,OAAO,CAAC,aAAa;qBACzB,CAAC;oBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;oBACxD,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;oBACxC,aAAa,EAAE,CAAC;gBAClB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC/E,OAAO,CAAC,IAAI,CAAC;wBACX,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,EAAE;wBACZ,SAAS,EAAE,EAAE;wBACb,SAAS,EAAE,CAAC;wBACZ,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;wBACnC,QAAQ,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;wBACxE,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACrC,YAAY,EAAE,EAAE;wBAChB,UAAU;qBACJ,CAAC,CAAC;oBACV,WAAW,EAAE,CAAC;gBAChB,CAAC;YACH,CAAC;YAGD,IAAI,OAAO,GAAG,SAAS,CAAC;YACxB,IAAI,OAAO,CAAC,cAAc,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBAChD,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,QAAQ,GAA2B;gBACvC,OAAO,EAAE,aAAa,GAAG,CAAC;gBAC1B,cAAc,EAAE,aAAa;gBAC7B,YAAY,EAAE,WAAW;gBACzB,OAAO;gBACP,OAAO;gBACP,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,aAAa,aAAa,WAAW,SAAS,CAAC,CAAC;YAC7F,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,OAAO,GAA0B,EAAE,CAAC;YAE1C,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;gBAC7B,IAAI,QAAQ,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;oBACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;oBAC3D,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAGpC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;oBACpD,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;oBAEvE,MAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,4BAAW,CAAC,GAAG,CAAC,CAAC;wBAC5D,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAAW,CAAC,IAAI,CAAC,CAAC,CAAC,4BAAW,CAAC,GAAG,CAAC;oBAEvI,OAAO,CAAC,IAAI,CAAC;wBACX,SAAS,EAAE,UAAU,KAAK,CAAC,WAAW,EAAE;wBACxC,UAAU;wBACV,QAAQ;wBACR,MAAM;wBACN,SAAS,EAAE,KAAK,CAAC,IAAI;wBACrB,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;wBAC1C,WAAW,EAAE,IAAI;wBACjB,YAAY,EAAE,4BAA4B,QAAQ,EAAE;qBACrD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAGD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9F,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACpE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC3D,MAAM,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACvC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;gBACxD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACpE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAc;QAC9C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,aAAa,GAAG,IAAA,qBAAK,EAAC,QAAQ,EAAE,IAAI,EAAE;gBAC1C,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;gBAC/B,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;aACnB,CAAC,CAAC;YAEH,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACvC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACvC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACjC,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,IAAI,CAAC;wBAEH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;wBACzC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAClB,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,MAAM,CAAC,IAAI,KAAK,CAAC,kCAAkC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBAC5E,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,KAAK,CAAC,kCAAkC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAClC,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAGH,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;oBAC1B,aAAa,CAAC,IAAI,EAAE,CAAC;oBACrB,MAAM,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC,EAAE,KAAK,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAA+D;QAGzF,MAAM,eAAe,GAAG,eAAe,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;QAEzG,OAAO;YACL,QAAQ,EAAE,eAAe;YACzB,SAAS,EAAE,oBAAoB,eAAe,EAAE;YAChD,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,oCAAoC,eAAe,EAAE;SACpE,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,WAAW;QACf,MAAM,kBAAkB,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAChE,MAAM,eAAe,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE5D,OAAO;YACL,MAAM,EAAE,kBAAkB,IAAI,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;YACvE,oBAAoB,EAAE,kBAAkB;YACxC,uBAAuB,EAAE,eAAe;SACzC,CAAC;IACJ,CAAC;CACF,CAAA;AA9RY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;;GACA,gBAAgB,CA8R5B"}