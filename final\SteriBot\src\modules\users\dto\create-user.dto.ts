import { Is<PERSON><PERSON>, IsE<PERSON>, <PERSON><PERSON>num, Is<PERSON><PERSON>al, IsDateString, Min<PERSON>eng<PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"
import { UserRole } from "../../../common/decorators/roles.decorator"

export class CreateUserDto {
  @ApiProperty({
    description: 'Unique user ID',
    example: 'user123'
  })
  @IsString()
  userId: string

  @ApiProperty({
    description: 'Username',
    example: 'walid'
  })
  @IsString()
  username: string

  @ApiProperty({
    description: 'User first name',
    example: 'John'
  })
  @IsString({ message: 'First name must be a string' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  firstName: string

  @ApiProperty({
    description: 'User last name',
    example: 'Doe'
  })
  @IsString({ message: 'Last name must be a string' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  lastName: string

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  email: string

  @ApiProperty({
    enum: UserRole,
    description: 'User role',
    example: UserRole.USER
  })
  @IsEnum(UserRole)
  role: UserRole

  @ApiProperty({
    required: false,
    description: 'Preferred language',
    example: 'en'
  })
  @IsOptional()
  @IsString()
  language?: string

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z',
    type: String
  })
  @IsOptional()
  @IsDateString({}, { message: 'createdAt must be a valid ISO 8601 date string' })
  createdAt?: string

  @ApiProperty({
    required: false,
    description: 'Last login date',
    example: '2024-01-01T00:00:00.000Z',
    type: String
  })
  @IsOptional()
  @IsDateString({}, { message: 'lastLogin must be a valid ISO 8601 date string' })
  lastLogin?: string

  @ApiProperty({
    required: false,
    description: 'Job title ID',
    example: 'jt_123'
  })
  @IsOptional()
  @IsString({ message: 'Job title ID must be a string' })
  jobTitleId?: string

  @ApiProperty({
    required: false,
    description: 'Department ID',
    example: 'dept_123'
  })
  @IsOptional()
  @IsString({ message: 'Department ID must be a string' })
  departmentId?: string

  @ApiProperty({
    required: false,
    description: 'User profile picture URL',
    example: 'https://example.com/profile.jpg'
  })
  @IsOptional()
  @IsString({ message: 'Picture URL must be a string' })
  picture?: string
}
