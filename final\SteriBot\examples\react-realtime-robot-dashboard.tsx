import React, { useState, useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

// Type definitions
interface RobotPosition {
  x: number;
  y: number;
  z: number;
  theta: number;
  frame_id: string;
  source_topic: string;
}

interface BatteryLevel {
  percentage?: number;
  voltage?: number;
  current?: number;
  temperature?: number;
  charging?: boolean;
  source_topic: string;
}

interface RealtimeRobotDataEvent {
  event_type: 'robot_data';
  robot_id: string;
  session_id: string;
  timestamp: string;
  position?: RobotPosition;
  battery_level?: BatteryLevel;
}

interface MapInfo {
  width: number;
  height: number;
  resolution: number;
  origin: { x: number; y: number; z: number };
}

interface RealtimeMapDataEvent {
  event_type: 'map_data';
  robot_id: string;
  session_id: string;
  timestamp: string;
  info?: MapInfo;
  data?: number[];
  statistics?: {
    total_cells: number;
    free_cells: number;
    occupied_cells: number;
    unknown_cells: number;
  };
}

// Main Dashboard Component
const RealtimeRobotDashboard: React.FC = () => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [robotData, setRobotData] = useState<RealtimeRobotDataEvent | null>(null);
  const [mapData, setMapData] = useState<RealtimeMapDataEvent | null>(null);
  const [activeStreams, setActiveStreams] = useState<string[]>([]);
  const [robotIP, setRobotIP] = useState('*************');
  const [isStreaming, setIsStreaming] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Initialize WebSocket connection
  useEffect(() => {
    const newSocket = io('/realtime-robot-data', {
      transports: ['websocket'],
      autoConnect: true,
    });

    newSocket.on('connect', () => {
      console.log('✅ Connected to real-time data stream');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('❌ Disconnected from real-time data stream');
      setIsConnected(false);
    });

    newSocket.on('robot_data', (data: RealtimeRobotDataEvent) => {
      console.log('Robot data received:', data);
      setRobotData(data);
    });

    newSocket.on('map_data', (data: RealtimeMapDataEvent) => {
      console.log('Map data received:', data);
      setMapData(data);
    });

    newSocket.on('stream_status', (status) => {
      console.log('Stream status:', status);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  // Render map on canvas when map data updates
  useEffect(() => {
    if (mapData && canvasRef.current) {
      renderMap(mapData);
    }
  }, [mapData]);

  const startStream = async () => {
    try {
      const response = await fetch('/api/robots/realtime/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ip_address: robotIP,
          port: 8765,
          include_robot_data: true,
          include_map_data: true,
          update_frequency: 2,
        }),
      });

      const result = await response.json();
      if (result.success) {
        setActiveStreams(prev => [...prev, result.session_id]);
        setIsStreaming(true);
        console.log('Stream started:', result.session_id);
      } else {
        console.error('Failed to start stream:', result.error);
      }
    } catch (error) {
      console.error('Error starting stream:', error);
    }
  };

  const stopStream = async () => {
    if (activeStreams.length === 0) return;

    try {
      const sessionId = activeStreams[activeStreams.length - 1];
      const response = await fetch('/api/robots/realtime/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
        }),
      });

      const result = await response.json();
      if (result.success) {
        setActiveStreams(prev => prev.filter(id => id !== sessionId));
        setIsStreaming(false);
        console.log('Stream stopped:', sessionId);
      }
    } catch (error) {
      console.error('Error stopping stream:', error);
    }
  };

  const exportMap = async () => {
    try {
      const response = await fetch('/api/robots/map/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ip_address: robotIP,
          port: 8765,
          format: 'png',
          color_scheme: 'colored',
          scale_factor: 2.0,
          include_grid: false,
          include_robot_position: true,
          filename: `robot_map_${robotIP.replace(/\./g, '_')}`,
        }),
      });

      const result = await response.json();
      if (result.success) {
        // Create download link
        const link = document.createElement('a');
        link.href = result.download_url;
        link.download = result.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        console.log('Map exported:', result.filename);
      }
    } catch (error) {
      console.error('Map export failed:', error);
    }
  };

  const renderMap = (mapEvent: RealtimeMapDataEvent) => {
    const canvas = canvasRef.current;
    if (!canvas || !mapEvent.info || !mapEvent.data) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = mapEvent.info;
    const data = mapEvent.data;

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    // Create image data
    const imageData = ctx.createImageData(width, height);
    const pixels = imageData.data;

    // Convert occupancy grid to RGBA
    for (let i = 0; i < data.length; i++) {
      const pixelIndex = i * 4;
      const occupancy = data[i];

      if (occupancy === -1) {
        // Unknown - Gray
        pixels[pixelIndex] = 128;
        pixels[pixelIndex + 1] = 128;
        pixels[pixelIndex + 2] = 128;
      } else if (occupancy === 0) {
        // Free - White
        pixels[pixelIndex] = 255;
        pixels[pixelIndex + 1] = 255;
        pixels[pixelIndex + 2] = 255;
      } else {
        // Occupied - Black
        pixels[pixelIndex] = 0;
        pixels[pixelIndex + 1] = 0;
        pixels[pixelIndex + 2] = 0;
      }
      pixels[pixelIndex + 3] = 255; // Alpha
    }

    // Draw to canvas
    ctx.putImageData(imageData, 0, 0);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🤖 Real-time Robot Dashboard</h1>
      
      {/* Connection Status */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Connection Status</h3>
        <div style={{ 
          padding: '10px', 
          backgroundColor: isConnected ? '#d4edda' : '#f8d7da',
          color: isConnected ? '#155724' : '#721c24',
          borderRadius: '5px'
        }}>
          {isConnected ? '✅ Connected to WebSocket' : '❌ Disconnected'}
        </div>
      </div>

      {/* Stream Controls */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Stream Controls</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <input
            type="text"
            value={robotIP}
            onChange={(e) => setRobotIP(e.target.value)}
            placeholder="Robot IP Address"
            style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
          />
          <button
            onClick={startStream}
            disabled={!isConnected || isStreaming}
            style={{
              padding: '8px 16px',
              backgroundColor: isStreaming ? '#6c757d' : '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isStreaming ? 'not-allowed' : 'pointer'
            }}
          >
            {isStreaming ? 'Streaming...' : 'Start Stream'}
          </button>
          <button
            onClick={stopStream}
            disabled={!isStreaming}
            style={{
              padding: '8px 16px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isStreaming ? 'pointer' : 'not-allowed'
            }}
          >
            Stop Stream
          </button>
          <button
            onClick={exportMap}
            disabled={!mapData}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: mapData ? 'pointer' : 'not-allowed'
            }}
          >
            Export Map
          </button>
        </div>
      </div>

      <div style={{ display: 'flex', gap: '20px' }}>
        {/* Robot Data Panel */}
        <div style={{ flex: 1 }}>
          <h3>🤖 Robot Data</h3>
          <div style={{ 
            border: '1px solid #ccc', 
            borderRadius: '5px', 
            padding: '15px',
            backgroundColor: '#f8f9fa'
          }}>
            {robotData ? (
              <div>
                <p><strong>Last Update:</strong> {formatTimestamp(robotData.timestamp)}</p>
                <p><strong>Robot ID:</strong> {robotData.robot_id}</p>
                
                {robotData.position && (
                  <div>
                    <h4>📍 Position</h4>
                    <p>X: {robotData.position.x.toFixed(3)}m</p>
                    <p>Y: {robotData.position.y.toFixed(3)}m</p>
                    <p>Θ: {robotData.position.theta.toFixed(3)}rad</p>
                    <p>Frame: {robotData.position.frame_id}</p>
                  </div>
                )}
                
                {robotData.battery_level && (
                  <div>
                    <h4>🔋 Battery</h4>
                    {robotData.battery_level.percentage !== undefined && (
                      <p>Level: {robotData.battery_level.percentage.toFixed(1)}%</p>
                    )}
                    {robotData.battery_level.voltage !== undefined && (
                      <p>Voltage: {robotData.battery_level.voltage.toFixed(2)}V</p>
                    )}
                    {robotData.battery_level.charging !== undefined && (
                      <p>Charging: {robotData.battery_level.charging ? 'Yes' : 'No'}</p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <p>No robot data received yet...</p>
            )}
          </div>
        </div>

        {/* Map Panel */}
        <div style={{ flex: 2 }}>
          <h3>🗺️ Map Data</h3>
          <div style={{ 
            border: '1px solid #ccc', 
            borderRadius: '5px', 
            padding: '15px',
            backgroundColor: '#f8f9fa'
          }}>
            {mapData ? (
              <div>
                <p><strong>Last Update:</strong> {formatTimestamp(mapData.timestamp)}</p>
                {mapData.info && (
                  <div>
                    <p><strong>Dimensions:</strong> {mapData.info.width} × {mapData.info.height}</p>
                    <p><strong>Resolution:</strong> {mapData.info.resolution}m/pixel</p>
                  </div>
                )}
                {mapData.statistics && (
                  <div>
                    <p><strong>Free Cells:</strong> {mapData.statistics.free_cells}</p>
                    <p><strong>Occupied Cells:</strong> {mapData.statistics.occupied_cells}</p>
                    <p><strong>Unknown Cells:</strong> {mapData.statistics.unknown_cells}</p>
                  </div>
                )}
                <div style={{ marginTop: '10px' }}>
                  <canvas
                    ref={canvasRef}
                    style={{
                      border: '1px solid #ddd',
                      maxWidth: '100%',
                      height: 'auto',
                      backgroundColor: 'white'
                    }}
                  />
                </div>
              </div>
            ) : (
              <p>No map data received yet...</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealtimeRobotDashboard;
