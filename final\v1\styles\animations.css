/**
 * Animation Styles - Keyframes and animation classes
 */

/* Gradient Animations */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradientTextShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes backgroundShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Fade Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse Animations */
.wifi-pulse {
  animation: wifiPulse 2s ease-in-out infinite;
}

@keyframes wifiPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(20, 184, 166, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 20px rgba(20, 184, 166, 0);
    transform: scale(1.05);
  }
}

/* Wave Animations */
.robot-waves {
  position: relative;
}

.robot-waves::before,
.robot-waves::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(20, 184, 166, 0.3);
  border-radius: 50%;
  animation: robotWaves 3s ease-out infinite;
}

.robot-waves::before {
  width: 120%;
  height: 120%;
  animation-delay: 0s;
}

.robot-waves::after {
  width: 140%;
  height: 140%;
  animation-delay: 1s;
}

@keyframes robotWaves {
  0% {
    width: 100%;
    height: 100%;
    opacity: 1;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

/* Flow Animations */
.flow-arrows {
  animation: flowArrows 2s ease-in-out infinite;
}

@keyframes flowArrows {
  0%, 100% {
    transform: translateX(0);
    opacity: 1;
  }
  50% {
    transform: translateX(10px);
    opacity: 0.7;
  }
}

/* Loading Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Bounce Animations */
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Slide Animations */
@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Scale Animations */
@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleOut {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

/* Utility Animation Classes */
.animate-slide-in-left {
  animation: slideInFromLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.5s ease-out;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.5s ease-out;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-scale-out {
  animation: scaleOut 0.3s ease-in;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Hover Animations */
.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.2s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(20, 184, 166, 0.4);
}
