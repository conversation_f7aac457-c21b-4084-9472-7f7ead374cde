// Comprehensive Firebase registration debugging script
const fetch = require('node-fetch');

async function debugFirebaseRegistration() {
  console.log('🔍 Firebase Registration Debug Tool\n');
  console.log('=' .repeat(50));
  
  const baseUrl = 'http://localhost:3001';
  
  // Step 1: Check if server is running
  console.log('\n📡 Step 1: Checking if server is running...');
  try {
    const healthResponse = await fetch(`${baseUrl}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}) // Empty body to trigger validation
    });
    console.log('✅ Server is running');
  } catch (error) {
    console.log('❌ Server is not running. Please start with: npm run start:dev');
    console.log('Error:', error.message);
    return;
  }
  
  // Step 2: Test registration with detailed logging
  console.log('\n🔐 Step 2: Testing user registration...');
  
  const timestamp = Date.now();
  const testUser = {
    username: 'debuguser',
    email: `debug${timestamp}@example.com`,
    password: 'debugpass123',
    role: 'user'
  };
  
  console.log('Test user data:', testUser);
  
  try {
    const registerResponse = await fetch(`${baseUrl}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testUser)
    });
    
    const registerData = await registerResponse.json();
    
    console.log('\n📊 Registration Response:');
    console.log('Status:', registerResponse.status);
    console.log('Response:', JSON.stringify(registerData, null, 2));
    
    if (registerResponse.ok) {
      console.log('✅ Registration API call successful');
      
      // Step 3: Wait and test login
      console.log('\n⏳ Step 3: Waiting 3 seconds for Firebase to process...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      console.log('\n🔑 Step 4: Testing login with registered credentials...');
      
      const loginResponse = await fetch(`${baseUrl}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password
        })
      });
      
      const loginData = await loginResponse.json();
      
      console.log('\n📊 Login Response:');
      console.log('Status:', loginResponse.status);
      console.log('Response:', JSON.stringify(loginData, null, 2));
      
      if (loginResponse.ok) {
        console.log('\n🎉 SUCCESS: Password was properly registered and login works!');
      } else {
        console.log('\n❌ ISSUE: Registration succeeded but login failed');
        console.log('This suggests the password was not properly stored in Firebase Auth');
        
        // Step 5: Test with wrong password to compare error
        console.log('\n🧪 Step 5: Testing with wrong password for comparison...');
        
        const wrongPasswordResponse = await fetch(`${baseUrl}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testUser.email,
            password: 'wrongpassword123'
          })
        });
        
        const wrongPasswordData = await wrongPasswordResponse.json();
        console.log('Wrong password response:', JSON.stringify(wrongPasswordData, null, 2));
        
        // Analyze the difference
        if (JSON.stringify(loginData) === JSON.stringify(wrongPasswordData)) {
          console.log('\n🚨 CRITICAL ISSUE: Same error for correct and wrong password!');
          console.log('This confirms the password was not stored in Firebase Auth');
        } else {
          console.log('\n🤔 Different errors - password might be stored but there\'s another issue');
        }
      }
      
    } else {
      console.log('❌ Registration failed');
      
      // Analyze the error
      if (registerData.message) {
        console.log('\n🔍 Error Analysis:');
        
        if (registerData.message.includes('email-already-exists')) {
          console.log('- Email already exists in Firebase Auth');
        } else if (registerData.message.includes('invalid-email')) {
          console.log('- Invalid email format');
        } else if (registerData.message.includes('weak-password')) {
          console.log('- Password is too weak (Firebase requires 6+ characters)');
        } else if (registerData.message.includes('certificate') || registerData.message.includes('network')) {
          console.log('- Network/SSL certificate issue');
        } else if (registerData.message.includes('Firebase initialization')) {
          console.log('- Firebase service not properly initialized');
        } else if (registerData.message.includes('permission')) {
          console.log('- Firebase service account lacks permissions');
        } else {
          console.log('- Unknown error:', registerData.message);
        }
      }
    }
    
  } catch (error) {
    console.log('❌ Registration test failed with network error:', error.message);
  }
  
  // Step 6: Provide troubleshooting suggestions
  console.log('\n' + '=' .repeat(50));
  console.log('🛠️  TROUBLESHOOTING CHECKLIST:');
  console.log('');
  console.log('1. ✅ Check Firebase Console:');
  console.log('   - Go to https://console.firebase.google.com/');
  console.log('   - Select your project (steribot-23c2c)');
  console.log('   - Go to Authentication → Sign-in method');
  console.log('   - Ensure "Email/Password" provider is ENABLED');
  console.log('');
  console.log('2. ✅ Check Environment Variables:');
  console.log('   - Ensure all Firebase credentials are in .env file');
  console.log('   - Verify FIREBASE_WEB_API_KEY is set');
  console.log('   - Check that private key format is correct');
  console.log('');
  console.log('3. ✅ Check Firebase Service Account:');
  console.log('   - Ensure service account has "Firebase Authentication Admin" role');
  console.log('   - Verify service account key is not expired');
  console.log('');
  console.log('4. ✅ Check Server Logs:');
  console.log('   - Look at the server console for detailed error messages');
  console.log('   - Check for "Firebase initialized successfully" message');
  console.log('');
  console.log('5. ✅ Test Firebase Console Directly:');
  console.log('   - Try creating a user manually in Firebase Console');
  console.log('   - Go to Authentication → Users → Add user');
}

// Run the debug tool
debugFirebaseRegistration().catch(console.error);
