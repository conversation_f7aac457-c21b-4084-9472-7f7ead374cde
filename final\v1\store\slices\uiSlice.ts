/**
 * UI Redux Slice
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Toast notification interface
export interface Toast {
  id: string;
  title?: string;
  description: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Modal interface
export interface Modal {
  id: string;
  type: string;
  isOpen: boolean;
  data?: any;
}

// Loading state interface
export interface LoadingState {
  [key: string]: boolean;
}

// UI state interface
export interface UIState {
  // Theme
  theme: 'light' | 'dark' | 'system';
  
  // Sidebar
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  
  // Mobile
  isMobile: boolean;
  
  // Toasts
  toasts: Toast[];
  
  // Modals
  modals: Modal[];
  
  // Loading states
  loading: LoadingState;
  
  // Page states
  currentPage: string;
  pageTitle: string;
  breadcrumbs: Array<{ label: string; href?: string }>;
  
  // Notifications
  notifications: Array<{
    id: string;
    title: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    timestamp: number;
    read: boolean;
  }>;
  
  // Search
  searchQuery: string;
  searchResults: any[];
  isSearching: boolean;
}

// Initial state
const initialState: UIState = {
  theme: 'system',
  sidebarOpen: true,
  sidebarCollapsed: false,
  isMobile: false,
  toasts: [],
  modals: [],
  loading: {},
  currentPage: '',
  pageTitle: '',
  breadcrumbs: [],
  notifications: [],
  searchQuery: '',
  searchResults: [],
  isSearching: false,
};

// UI slice
export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Theme actions
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },
    
    // Sidebar actions
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    
    toggleSidebarCollapsed: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    
    // Mobile actions
    setIsMobile: (state, action: PayloadAction<boolean>) => {
      state.isMobile = action.payload;
    },
    
    // Toast actions
    addToast: (state, action: PayloadAction<Omit<Toast, 'id'>>) => {
      const toast: Toast = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    
    removeToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },
    
    clearToasts: (state) => {
      state.toasts = [];
    },
    
    // Modal actions
    openModal: (state, action: PayloadAction<{ type: string; data?: any }>) => {
      const modal: Modal = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: action.payload.type,
        isOpen: true,
        data: action.payload.data,
      };
      state.modals.push(modal);
    },
    
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals = state.modals.filter(modal => modal.id !== action.payload);
    },
    
    closeAllModals: (state) => {
      state.modals = [];
    },
    
    // Loading actions
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading;
    },
    
    clearLoading: (state, action: PayloadAction<string>) => {
      delete state.loading[action.payload];
    },
    
    clearAllLoading: (state) => {
      state.loading = {};
    },
    
    // Page actions
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload;
    },
    
    setBreadcrumbs: (state, action: PayloadAction<Array<{ label: string; href?: string }>>) => {
      state.breadcrumbs = action.payload;
    },
    
    // Notification actions
    addNotification: (state, action: PayloadAction<Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>>) => {
      const notification = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: Date.now(),
        read: false,
        ...action.payload,
      };
      state.notifications.unshift(notification);
    },
    
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // Search actions
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    
    setSearchResults: (state, action: PayloadAction<any[]>) => {
      state.searchResults = action.payload;
    },
    
    setIsSearching: (state, action: PayloadAction<boolean>) => {
      state.isSearching = action.payload;
    },
    
    clearSearch: (state) => {
      state.searchQuery = '';
      state.searchResults = [];
      state.isSearching = false;
    },
  },
});

// Export actions
export const {
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapsed,
  setSidebarCollapsed,
  setIsMobile,
  addToast,
  removeToast,
  clearToasts,
  openModal,
  closeModal,
  closeAllModals,
  setLoading,
  clearLoading,
  clearAllLoading,
  setCurrentPage,
  setPageTitle,
  setBreadcrumbs,
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  removeNotification,
  clearNotifications,
  setSearchQuery,
  setSearchResults,
  setIsSearching,
  clearSearch,
} = uiSlice.actions;

// Export selectors
export const selectTheme = (state: any) => state.ui.theme;
export const selectSidebarOpen = (state: any) => state.ui.sidebarOpen;
export const selectSidebarCollapsed = (state: any) => state.ui.sidebarCollapsed;
export const selectIsMobile = (state: any) => state.ui.isMobile;
export const selectToasts = (state: any) => state.ui.toasts;
export const selectModals = (state: any) => state.ui.modals;
export const selectLoading = (state: any) => state.ui.loading;
export const selectIsLoading = (key: string) => (state: any) => state.ui.loading[key] || false;
export const selectCurrentPage = (state: any) => state.ui.currentPage;
export const selectPageTitle = (state: any) => state.ui.pageTitle;
export const selectBreadcrumbs = (state: any) => state.ui.breadcrumbs;
export const selectNotifications = (state: any) => state.ui.notifications;
export const selectUnreadNotifications = (state: any) => state.ui.notifications.filter((n: any) => !n.read);
export const selectSearchQuery = (state: any) => state.ui.searchQuery;
export const selectSearchResults = (state: any) => state.ui.searchResults;
export const selectIsSearching = (state: any) => state.ui.isSearching;

// Export reducer
export default uiSlice.reducer;
