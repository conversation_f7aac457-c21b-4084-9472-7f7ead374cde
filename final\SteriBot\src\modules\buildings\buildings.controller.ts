import { <PERSON>, Get, Post, Patch, Param, Delete, UseGuards, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import { BuildingsService } from "./buildings.service"
import { CreateBuildingDto } from "./dto/create-building.dto"
import { UpdateBuildingDto } from "./dto/update-building.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Buildings")
@Controller("buildings")
export class BuildingsController {
  constructor(private readonly buildingsService: BuildingsService) {}

  @Post()
  @ApiOperation({ summary: "Create a new building" })
  create(@Body() createBuildingDto: CreateBuildingDto) {
    return this.buildingsService.create(createBuildingDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all buildings" })
  findAll() {
    return this.buildingsService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get building by ID' })
  findOne(@Param('id') id: string) {
    return this.buildingsService.findById(id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update building" })
  update(@Param('id') id: string, @Body() updateBuildingDto: UpdateBuildingDto) {
    return this.buildingsService.update(id, updateBuildingDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete building' })
  remove(@Param('id') id: string) {
    return this.buildingsService.remove(id);
  }
}
