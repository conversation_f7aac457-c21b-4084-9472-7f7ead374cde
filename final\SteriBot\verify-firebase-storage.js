// <PERSON>ript to verify Firebase Storage setup
const admin = require('firebase-admin');
require('dotenv').config();

async function verifyFirebaseStorage() {
  console.log('🔍 Firebase Storage Verification Tool\n');
  console.log('=' .repeat(60));
  
  // Check environment variables
  console.log('\n📋 Step 1: Checking environment variables...');
  
  const requiredVars = [
    'FIREBASE_PROJECT_ID',
    'FIREBASE_PRIVATE_KEY_ID', 
    'FIREBASE_PRIVATE_KEY',
    'FIREBASE_CLIENT_EMAIL',
    'FIREBASE_CLIENT_ID',
    'FIREBASE_STORAGE_BUCKET'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log('❌ Missing environment variables:');
    missingVars.forEach(varName => console.log(`   - ${varName}`));
    return;
  }
  
  console.log('✅ All required environment variables are present');
  console.log(`Project ID: ${process.env.FIREBASE_PROJECT_ID}`);
  console.log(`Storage Bucket: ${process.env.FIREBASE_STORAGE_BUCKET}`);
  
  // Initialize Firebase Admin
  console.log('\n🔥 Step 2: Initializing Firebase Admin...');
  
  try {
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: process.env.FIREBASE_CLIENT_X509_CERT_URL,
    };

    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: process.env.FIREBASE_DATABASE_URL,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      });
    }
    
    console.log('✅ Firebase Admin initialized successfully');
  } catch (error) {
    console.log('❌ Firebase Admin initialization failed:', error.message);
    return;
  }
  
  // Test Storage Access
  console.log('\n📦 Step 3: Testing Storage access...');
  
  try {
    const storage = admin.storage();
    const bucket = storage.bucket();
    
    console.log(`Bucket name: ${bucket.name}`);
    
    // Try to check if bucket exists
    const [exists] = await bucket.exists();
    
    if (exists) {
      console.log('✅ Storage bucket exists and is accessible');
      
      // Try to list files (just to test permissions)
      console.log('\n📁 Step 4: Testing bucket permissions...');
      try {
        const [files] = await bucket.getFiles({ maxResults: 1 });
        console.log('✅ Bucket permissions are working');
        console.log(`Found ${files.length} files (showing max 1 for test)`);
      } catch (permError) {
        console.log('⚠️  Bucket exists but permission test failed:', permError.message);
      }
      
    } else {
      console.log('❌ Storage bucket does not exist');
      console.log('\n🚨 ISSUE IDENTIFIED: Firebase Storage bucket not found');
      console.log('\n📋 TO FIX:');
      console.log('1. Go to Firebase Console: https://console.firebase.google.com/');
      console.log(`2. Select your project: ${process.env.FIREBASE_PROJECT_ID}`);
      console.log('3. Go to Storage in the left sidebar');
      console.log('4. Click "Get started" if Storage is not enabled');
      console.log('5. Choose "Start in production mode" or "Start in test mode"');
      console.log('6. Select a location for your storage bucket');
      console.log('7. The bucket will be created automatically');
      console.log('\nAlternatively, the bucket name might be different.');
      console.log('Check your Firebase Console Storage section for the actual bucket name.');
    }
    
  } catch (error) {
    console.log('❌ Storage access failed:', error.message);
    
    if (error.message.includes('does not exist')) {
      console.log('\n🚨 ISSUE: Storage bucket does not exist');
      console.log('\n📋 SOLUTIONS:');
      console.log('1. Enable Firebase Storage in Firebase Console');
      console.log('2. Check if bucket name is correct in .env file');
      console.log('3. Verify service account has Storage permissions');
    } else if (error.message.includes('permission')) {
      console.log('\n🚨 ISSUE: Permission denied');
      console.log('\n📋 TO FIX:');
      console.log('1. Go to Firebase Console → Project Settings → Service Accounts');
      console.log('2. Make sure your service account has "Storage Admin" role');
      console.log('3. Or add "Storage Object Admin" permission');
    }
  }
  
  // Test file upload
  console.log('\n📤 Step 5: Testing file upload...');
  
  try {
    const storage = admin.storage();
    const bucket = storage.bucket();
    
    // Create a test file
    const testFileName = `test-upload-${Date.now()}.txt`;
    const testContent = 'This is a test file for Firebase Storage verification';
    const file = bucket.file(`test-uploads/${testFileName}`);
    
    await file.save(Buffer.from(testContent), {
      metadata: {
        contentType: 'text/plain',
      },
    });
    
    console.log('✅ Test file upload successful');
    console.log(`Test file: test-uploads/${testFileName}`);
    
    // Clean up test file
    await file.delete();
    console.log('✅ Test file cleanup successful');
    
  } catch (uploadError) {
    console.log('❌ File upload test failed:', uploadError.message);
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 SUMMARY:');
  console.log('');
  console.log('If you see errors above, follow these steps:');
  console.log('1. Enable Firebase Storage in Firebase Console');
  console.log('2. Verify service account permissions');
  console.log('3. Check bucket name in .env file');
  console.log('4. Restart your application after making changes');
  console.log('');
  console.log('For more help, see: FIREBASE_SETUP.md');
}

// Run verification
verifyFirebaseStorage().catch(console.error);
