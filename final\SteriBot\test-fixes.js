#!/usr/bin/env node
/**
 * Test the fixes for download URL and Python WebSocket connection
 */

const http = require('http');

const TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MMHmjt-VMwM9trwqTM0m6fkZgrBEO62BvhV-zbz2kJbsC_TG6HPF9j6hsd_ZymcxwuO9XoxJo8qdwJP5Gks51vSZUClo1dD0Dn2kSSjV1NP0OKlsxaKbkjKx2qnr_kLcvY5dT2O6DcHNcCsUbADt3x0I_TBJQVc4OOhb-r0nkIsTc5EOGeJAG2qMrh05lkvfsSAJUx86k8punaO21hXB2tpcXZRlZmgL7tmI7IULYe8FMCkN0xIUqz3p2mEwPNHQSZrwlAZ1iQaDmT5rqXuWQLEqUiXslKXin-Xl2GsJh_v5iziJRTv95bBupvN-58o74ESYDQ2-JReaGyVkPhmqwQ';

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testFixes() {
  console.log('🔧 Testing Fixes');
  console.log('=' .repeat(50));
  
  try {
    // Test 1: Save Map and check download URL
    console.log('1. Testing Save Map functionality...');
    const saveResult = await makeRequest('/api/v1/robots/map/save', 'POST');
    console.log(`   Status: ${saveResult.status}`);
    
    if (saveResult.status === 201 && saveResult.data.success) {
      console.log('   ✅ Map save successful!');
      console.log(`   📁 Filename: ${saveResult.data.filename}`);
      console.log(`   🔗 Download URL: ${saveResult.data.download_url}`);
      
      // Test the download URL (should not have double /api/v1/)
      if (saveResult.data.download_url.includes('/api/v1/api/v1/')) {
        console.log('   ❌ Download URL still has double prefix!');
      } else {
        console.log('   ✅ Download URL format looks correct');
      }
      
      // Test 2: Try to access the download URL
      console.log('\n2. Testing download URL access...');
      const downloadResult = await makeRequest(saveResult.data.download_url);
      console.log(`   Download Status: ${downloadResult.status}`);
      
      if (downloadResult.status === 200) {
        console.log('   ✅ Download URL accessible!');
      } else {
        console.log(`   ❌ Download failed: ${downloadResult.data}`);
      }
      
    } else {
      console.log('   ❌ Map save failed');
      console.log(`   Error: ${JSON.stringify(saveResult.data)}`);
    }
    
    // Test 3: Test ROS2 connection (should work with fixed Python script)
    console.log('\n3. Testing ROS2 connection...');
    const connectionResult = await makeRequest('/api/v1/robots/test/connection', 'POST');
    console.log(`   Status: ${connectionResult.status}`);
    
    if (connectionResult.status === 201 && connectionResult.data.success) {
      console.log('   ✅ ROS2 connection test successful!');
      console.log(`   📍 Position available: ${connectionResult.data.connection_test.has_position}`);
      console.log(`   🔋 Battery available: ${connectionResult.data.connection_test.has_battery}`);
    } else {
      console.log('   ❌ ROS2 connection failed');
      console.log(`   Error: ${JSON.stringify(connectionResult.data)}`);
    }
    
  } catch (error) {
    console.log(`❌ Test error: ${error.message}`);
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 Fix Test Complete');
  console.log('');
  console.log('Expected Results:');
  console.log('✅ Map save should work without download URL errors');
  console.log('✅ Python WebSocket connection should work without timeout errors');
  console.log('✅ Real-time data should start flowing to frontend');
}

testFixes();
