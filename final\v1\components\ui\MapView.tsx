// components/MapView.tsx
import { useRobotData } from "@/hooks/use-robot-data";
import { useState, useEffect } from "react";

interface MapViewProps {
  onMapClick?: () => void;
  isClickable?: boolean;
}

export function MapView({ onMapClick, isClickable = false }: MapViewProps) {
  const { mapData } = useRobotData();
  const [latestMapUrl, setLatestMapUrl] = useState<string | null>(null);
  const [mapImageData, setMapImageData] = useState<string | null>(null);

  const handleClick = () => {
    if (isClickable && onMapClick) {
      onMapClick();
    }
  };

  // Convert map data to image for display
  useEffect(() => {
    if (mapData && mapData.data && mapData.width && mapData.height) {
      try {
        // Create canvas to render map data
        const canvas = document.createElement('canvas');
        canvas.width = mapData.width;
        canvas.height = mapData.height;
        const ctx = canvas.getContext('2d');

        if (ctx) {
          const imageData = ctx.createImageData(mapData.width, mapData.height);

          // Convert occupancy grid to image data
          for (let i = 0; i < mapData.data.length; i++) {
            const value = mapData.data[i];
            let color = 128; // Unknown (gray)

            if (value === 0) color = 255; // Free space (white)
            else if (value === 100) color = 0; // Occupied (black)

            const pixelIndex = i * 4;
            imageData.data[pixelIndex] = color;     // R
            imageData.data[pixelIndex + 1] = color; // G
            imageData.data[pixelIndex + 2] = color; // B
            imageData.data[pixelIndex + 3] = 255;   // A
          }

          ctx.putImageData(imageData, 0, 0);
          setMapImageData(canvas.toDataURL());
        }
      } catch (error) {
        console.error('Error rendering map data:', error);
      }
    }
  }, [mapData]);



  // Check for latest saved map
  useEffect(() => {
    // This would typically fetch the latest map from the server
    // For now, we'll use a placeholder or the real-time map data
    if (isClickable && !latestMapUrl) {
      // Try to get the latest saved map
      // This could be enhanced to fetch from an API endpoint that lists saved maps
      setLatestMapUrl('/api/placeholder-map.png');
    }
  }, [isClickable, latestMapUrl]);

  const displayImageSrc = mapImageData || latestMapUrl;

  return (
    <div
      className={`bg-gray-700 rounded-lg overflow-hidden flex-1 relative ${
        isClickable ? 'cursor-pointer hover:bg-gray-600 transition-colors' : ''
      }`}
      onClick={handleClick}
    >
      {displayImageSrc ? (
        <img
          src={displayImageSrc}
          alt="Robot Map"
          className="w-full h-full object-contain"
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="text-4xl mb-2">🗺️</div>
            <div className="text-sm">
              {isClickable ? 'Saved map will appear here' : 'Real-time map will appear here'}
            </div>
            {mapData && (
              <div className="text-xs mt-2 text-gray-500">
                Map size: {mapData.width}x{mapData.height}
                <br />
                Resolution: {mapData.resolution}m/pixel
                <br />
                Last update: {mapData.timestamp ? new Date(mapData.timestamp).toLocaleTimeString() : 'Never'}
              </div>
            )}
          </div>
        </div>
      )}

      {isClickable && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity rounded-lg">
          <div className="bg-gray-800 text-white px-4 py-2 rounded-lg text-sm">
            Click to configure space
          </div>
        </div>
      )}

      {/* Map info overlay */}
      {mapData && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
          {mapData.width}×{mapData.height} | {mapData.resolution}m/px
        </div>
      )}
    </div>
  )
}