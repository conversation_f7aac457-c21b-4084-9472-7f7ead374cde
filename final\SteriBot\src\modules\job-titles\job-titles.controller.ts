import { <PERSON>, Get, Post, Patch, Param, Delete, UseGuards, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import { JobTitlesService } from "./job-titles.service"
import { CreateJobTitleDto } from "./dto/create-job-title.dto"
import { UpdateJobTitleDto } from "./dto/update-job-title.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Job Titles")
@Controller("job-titles")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JobTitlesController {
  constructor(private readonly jobTitlesService: JobTitlesService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Create a new job title (Admin only)" })
  create(@Body() createJobTitleDto: CreateJobTitleDto) {
    return this.jobTitlesService.create(createJobTitleDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all job titles" })
  findAll() {
    return this.jobTitlesService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get job title by ID' })
  findOne(@Param('id') id: string) {
    return this.jobTitlesService.findById(id)
  }

  @Get('department/:departmentId')
  @ApiOperation({ summary: 'Get job titles by department' })
  findByDepartment(@Param('departmentId') departmentId: string) {
    return this.jobTitlesService.findByDepartment(departmentId)
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update job title (Admin only)" })
  update(@Param('id') id: string, @Body() updateJobTitleDto: UpdateJobTitleDto) {
    return this.jobTitlesService.update(id, updateJobTitleDto)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete job title (Admin only)' })
  remove(@Param('id') id: string) {
    return this.jobTitlesService.remove(id)
  }
}
