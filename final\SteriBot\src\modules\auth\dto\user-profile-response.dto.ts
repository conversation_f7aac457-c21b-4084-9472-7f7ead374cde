import { ApiProperty } from "@nestjs/swagger"
import { UserRole } from "../../../common/decorators/roles.decorator"

export class JobTitleDto {
  @ApiProperty({
    description: 'Job title ID',
    example: 'jt_123'
  })
  id: string

  @ApiProperty({
    description: 'Job title name',
    example: 'Software Engineer'
  })
  name: string

  @ApiProperty({
    description: 'Job title description',
    example: 'Responsible for developing and maintaining software applications'
  })
  description?: string
}

export class DepartmentDto {
  @ApiProperty({
    description: 'Department ID',
    example: 'dept_123'
  })
  id: string

  @ApiProperty({
    description: 'Department name',
    example: 'Engineering'
  })
  name: string

  @ApiProperty({
    description: 'Department description',
    example: 'Software development and engineering team'
  })
  description?: string
}

export class UserProfileResponseDto {
  @ApiProperty({
    description: 'Unique user identifier',
    example: 'btNUn3XDVgRtuEWa4K6MzPax7lk2'
  })
  userId: string

  @ApiProperty({
    description: 'Username',
    example: 'nawel'
  })
  username: string

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>'
  })
  firstName: string

  @ApiProperty({
    description: 'User last name',
    example: 'Doe'
  })
  lastName: string

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  email: string

  @ApiProperty({
    enum: UserRole,
    description: 'User role',
    example: UserRole.USER
  })
  role: UserRole

  @ApiProperty({
    description: 'Preferred language',
    example: 'en'
  })
  language: string

  @ApiProperty({
    type: JobTitleDto,
    description: 'User job title information',
    required: false
  })
  jobTitle?: JobTitleDto

  @ApiProperty({
    type: DepartmentDto,
    description: 'User department information',
    required: false
  })
  department?: DepartmentDto

  @ApiProperty({
    description: 'Last login timestamp',
    example: '2025-07-30T09:24:06.000Z',
    required: false
  })
  lastLogin?: string

  @ApiProperty({
    description: 'User profile picture URL',
    example: 'https://example.com/profile.jpg',
    required: false
  })
  picture?: string
}
