"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserProfileResponseDto = exports.DepartmentDto = exports.JobTitleDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const roles_decorator_1 = require("../../../common/decorators/roles.decorator");
class JobTitleDto {
}
exports.JobTitleDto = JobTitleDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job title ID',
        example: 'jt_123'
    }),
    __metadata("design:type", String)
], JobTitleDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job title name',
        example: 'Software Engineer'
    }),
    __metadata("design:type", String)
], JobTitleDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job title description',
        example: 'Responsible for developing and maintaining software applications'
    }),
    __metadata("design:type", String)
], JobTitleDto.prototype, "description", void 0);
class DepartmentDto {
}
exports.DepartmentDto = DepartmentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Department ID',
        example: 'dept_123'
    }),
    __metadata("design:type", String)
], DepartmentDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Department name',
        example: 'Engineering'
    }),
    __metadata("design:type", String)
], DepartmentDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Department description',
        example: 'Software development and engineering team'
    }),
    __metadata("design:type", String)
], DepartmentDto.prototype, "description", void 0);
class UserProfileResponseDto {
}
exports.UserProfileResponseDto = UserProfileResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique user identifier',
        example: 'btNUn3XDVgRtuEWa4K6MzPax7lk2'
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Username',
        example: 'nawel'
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User first name',
        example: 'John'
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User last name',
        example: 'Doe'
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email address',
        example: '<EMAIL>'
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        enum: roles_decorator_1.UserRole,
        description: 'User role',
        example: roles_decorator_1.UserRole.USER
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Preferred language',
        example: 'en'
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: JobTitleDto,
        description: 'User job title information',
        required: false
    }),
    __metadata("design:type", JobTitleDto)
], UserProfileResponseDto.prototype, "jobTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: DepartmentDto,
        description: 'User department information',
        required: false
    }),
    __metadata("design:type", DepartmentDto)
], UserProfileResponseDto.prototype, "department", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last login timestamp',
        example: '2025-07-30T09:24:06.000Z',
        required: false
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "lastLogin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User profile picture URL',
        example: 'https://example.com/profile.jpg',
        required: false
    }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "picture", void 0);
//# sourceMappingURL=user-profile-response.dto.js.map