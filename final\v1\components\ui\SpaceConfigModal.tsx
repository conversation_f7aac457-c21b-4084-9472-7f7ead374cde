// components/ui/SpaceConfigModal.tsx
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface SpaceConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: SpaceConfigData) => void;
}

export interface SpaceConfigData {
  pseudo: string;
  spaceName: string;
  color: string;
}

const predefinedColors = [
  { name: 'Red', value: '#ef4444' },
  { name: 'Blue', value: '#3b82f6' },
  { name: 'Green', value: '#22c55e' },
  { name: 'Yellow', value: '#eab308' },
  { name: 'Orange', value: '#f97316' },
  { name: '<PERSON>', value: '#a855f7' },
  { name: '<PERSON>', value: '#ec4899' },
  { name: 'Teal', value: '#14b8a6' },
  { name: 'Indigo', value: '#6366f1' },
  { name: '<PERSON><PERSON>', value: '#06b6d4' }
];

export function SpaceConfigModal({ isOpen, onClose, onConfirm }: SpaceConfigModalProps) {
  const [pseudo, setPseudo] = useState('');
  const [spaceName, setSpaceName] = useState('');
  const [selectedColor, setSelectedColor] = useState(predefinedColors[0].value);
  const [customColor, setCustomColor] = useState('#000000');
  const [isCustomColorSelected, setIsCustomColorSelected] = useState(false);

  const handleConfirm = () => {
    if (!pseudo.trim() || !spaceName.trim()) return;

    onConfirm({
      pseudo: pseudo.trim(),
      spaceName: spaceName.trim(),
      color: isCustomColorSelected ? customColor : selectedColor
    });

    // Reset form
    setPseudo('');
    setSpaceName('');
    setSelectedColor(predefinedColors[0].value);
    setCustomColor('#000000');
    setIsCustomColorSelected(false);
  };

  const handleCancel = () => {
    setPseudo('');
    setSpaceName('');
    setSelectedColor(predefinedColors[0].value);
    setCustomColor('#000000');
    setIsCustomColorSelected(false);
    onClose();
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setIsCustomColorSelected(false);
  };

  const handleCustomColorSelect = () => {
    setIsCustomColorSelected(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-md text-white border-[#0C6980]"
        style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-center text-white">
            Enter Space Name
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Pseudo Input */}
          <div className="space-y-2">
            <Label htmlFor="pseudo" className="text-sm font-medium text-white">
              Pseudo
            </Label>
            <Input
              id="pseudo"
              placeholder="Pseudo : Exp: 85"
              value={pseudo}
              onChange={(e) => setPseudo(e.target.value)}
              className="bg-[#0A3F4C] border-[#0C6980] text-white placeholder-gray-300 focus:border-white"
            />
          </div>

          {/* Space Name Input */}
          <div className="space-y-2">
            <Label htmlFor="spaceName" className="text-sm font-medium text-white">
              Space Name
            </Label>
            <Input
              id="spaceName"
              placeholder="Space Name"
              value={spaceName}
              onChange={(e) => setSpaceName(e.target.value)}
              className="bg-[#0A3F4C] border-[#0C6980] text-white placeholder-gray-300 focus:border-white"
            />
          </div>

          {/* Color Picker Section */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-white">Color</Label>

            {/* Predefined Colors */}
            <div className="grid grid-cols-5 gap-2">
              {predefinedColors.map((color) => (
                <button
                  key={color.value}
                  onClick={() => handleColorSelect(color.value)}
                  className={`w-12 h-12 rounded-lg border-2 transition-all hover:scale-105 ${
                    selectedColor === color.value && !isCustomColorSelected
                      ? 'border-white shadow-lg'
                      : 'border-[#0C6980] hover:border-white'
                  }`}
                  style={{ backgroundColor: color.value }}
                  title={color.name}
                />
              ))}
            </div>

            {/* Custom Color Option */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleCustomColorSelect}
                className={`w-12 h-12 rounded-lg border-2 transition-all hover:scale-105 ${
                  isCustomColorSelected
                    ? 'border-white shadow-lg'
                    : 'border-[#0C6980] hover:border-white'
                }`}
                style={{ backgroundColor: customColor }}
                title="Custom Color"
              />
              <div className="flex-1">
                <Label htmlFor="customColor" className="text-sm text-white">
                  Custom Color
                </Label>
                <Input
                  id="customColor"
                  type="color"
                  value={customColor}
                  onChange={(e) => {
                    setCustomColor(e.target.value);
                    setIsCustomColorSelected(true);
                  }}
                  className="w-full h-10 bg-[#0A3F4C] border-[#0C6980]"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex-1 border-white text-black hover:bg-[#0C6980] hover:border-[#0C6980]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!pseudo.trim() || !spaceName.trim()}
            className="flex-1 text-white disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ background: 'linear-gradient(90deg, #0C6980, #14b8a6)' }}
          >
            Confirm
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}