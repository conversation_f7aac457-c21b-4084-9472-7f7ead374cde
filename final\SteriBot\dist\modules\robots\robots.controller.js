"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RobotsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RobotsController = void 0;
const common_1 = require("@nestjs/common");
const path = require("path");
const fs = require("fs");
const swagger_1 = require("@nestjs/swagger");
const robots_service_1 = require("./robots.service");
const create_robot_dto_1 = require("./dto/create-robot.dto");
const update_robot_dto_1 = require("./dto/update-robot.dto");
const get_robot_data_dto_1 = require("./dto/get-robot-data.dto");
const get_map_data_dto_1 = require("./dto/get-map-data.dto");
const realtime_data_dto_1 = require("./dto/realtime-data.dto");
const map_export_dto_1 = require("./dto/map-export.dto");
const firebase_auth_guard_1 = require("../../common/guards/firebase-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const realtime_data_service_1 = require("./services/realtime-data.service");
const map_export_service_1 = require("./services/map-export.service");
let RobotsController = RobotsController_1 = class RobotsController {
    constructor(robotsService, realtimeDataService, mapExportService) {
        this.robotsService = robotsService;
        this.realtimeDataService = realtimeDataService;
        this.mapExportService = mapExportService;
        this.logger = new common_1.Logger(RobotsController_1.name);
    }
    create(createRobotDto) {
        return this.robotsService.create(createRobotDto);
    }
    findAll() {
        return this.robotsService.findAll();
    }
    findOne(id) {
        return this.robotsService.findById(id);
    }
    update(id, updateRobotDto) {
        return this.robotsService.update(id, updateRobotDto);
    }
    updateStatus(id, status) {
        return this.robotsService.updateStatus(id, status);
    }
    remove(id) {
        return this.robotsService.remove(id);
    }
    async retrieveRobotData(request) {
        try {
            this.logger.log(`API: Retrieving robot data from ${request.ip_address}:${request.port || 9090}`);
            const result = await this.robotsService.getRobotData(request);
            this.logger.log(`API: Robot data retrieval completed - Status: ${result.connection_status}, ` +
                `Position: ${result.position ? 'Yes' : 'No'}, Battery: ${result.battery_level ? 'Yes' : 'No'}`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Robot data retrieval failed', error);
            throw new common_1.HttpException({
                message: 'Failed to retrieve robot data',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async retrieveMapData(request) {
        try {
            this.logger.log(`API: Retrieving map data from ${request.ip_address}:${request.port || 9090}`);
            const result = await this.robotsService.getMapData(request);
            this.logger.log(`API: Map data retrieval completed - Status: ${result.connection_status}, ` +
                `Has Data: ${result.has_map_data}, Data Length: ${result.data_length}`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Map data retrieval failed', error);
            throw new common_1.HttpException({
                message: 'Failed to retrieve map data',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async robotDataHealthCheck() {
        try {
            const result = await this.robotsService.robotDataHealthCheck();
            this.logger.log(`API: Robot data health check - Status: ${result.status}`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Robot data health check failed', error);
            throw new common_1.HttpException({
                message: 'Health check failed',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getActiveStreams() {
        try {
            const streams = this.realtimeDataService.getActiveStreams();
            this.logger.log(`API: Retrieved ${streams.length} active streams`);
            return streams;
        }
        catch (error) {
            this.logger.error('API: Failed to get active streams', error);
            throw new common_1.HttpException({
                message: 'Failed to get active streams',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async startRealtimeStream(request) {
        try {
            this.logger.log(`API: Starting realtime stream for ${request.ip_address}:${request.port || 9090}`);
            const config = {
                ip_address: request.ip_address,
                port: request.port || 9090,
                include_robot_data: request.include_robot_data ?? true,
                include_map_data: request.include_map_data ?? true,
                update_frequency: request.update_frequency || 2,
                session_id: `stream_${request.ip_address}_${request.port || 9090}_${Date.now()}`,
            };
            const result = await this.realtimeDataService.startStream(config);
            if (result.success) {
                this.logger.log(`API: Realtime stream started successfully: ${config.session_id}`);
                return {
                    success: true,
                    session_id: config.session_id,
                    message: 'Real-time stream started successfully',
                    websocket_info: {
                        namespace: '/realtime-robot-data',
                        events: ['robot_data', 'map_data', 'connection', 'stream_status'],
                        instructions: 'Connect to WebSocket to receive real-time data events',
                    },
                };
            }
            else {
                throw new common_1.HttpException({
                    message: 'Failed to start real-time stream',
                    error: result.error,
                    timestamp: new Date().toISOString(),
                }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
        catch (error) {
            this.logger.error('API: Failed to start realtime stream', error);
            throw new common_1.HttpException({
                message: 'Failed to start real-time stream',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async stopRealtimeStream(request) {
        try {
            this.logger.log(`API: Stopping realtime stream: ${request.session_id}`);
            const result = await this.realtimeDataService.stopStream(request.session_id);
            if (result.success) {
                this.logger.log(`API: Realtime stream stopped successfully: ${request.session_id}`);
                return {
                    success: true,
                    session_id: request.session_id,
                    message: 'Real-time stream stopped successfully',
                };
            }
            else {
                throw new common_1.HttpException({
                    message: 'Failed to stop real-time stream',
                    error: result.error,
                    timestamp: new Date().toISOString(),
                }, common_1.HttpStatus.BAD_REQUEST);
            }
        }
        catch (error) {
            this.logger.error('API: Failed to stop realtime stream', error);
            throw new common_1.HttpException({
                message: 'Failed to stop real-time stream',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async exportMapImage(request) {
        try {
            this.logger.log(`API: Exporting map from ${request.ip_address}:${request.port || 9090}`);
            const result = await this.mapExportService.exportMapImage(request);
            this.logger.log(`API: Map export completed: ${result.filename}`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Map export failed', error);
            throw new common_1.HttpException({
                message: 'Map export failed',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async batchExportMaps(request) {
        try {
            this.logger.log(`API: Batch exporting maps from ${request.ip_addresses.length} robots`);
            const result = await this.mapExportService.batchExportMaps(request);
            this.logger.log(`API: Batch export completed: ${result.exported_count} success, ${result.failed_count} failed`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Batch map export failed', error);
            throw new common_1.HttpException({
                message: 'Batch map export failed',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getMapExportHistory() {
        try {
            const history = await this.mapExportService.getExportHistory();
            this.logger.log(`API: Retrieved ${history.length} export history entries`);
            return history;
        }
        catch (error) {
            this.logger.error('API: Failed to get export history', error);
            throw new common_1.HttpException({
                message: 'Failed to get export history',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async mapExportHealthCheck() {
        try {
            const result = await this.mapExportService.healthCheck();
            this.logger.log(`API: Map export health check - Status: ${result.status}`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Map export health check failed', error);
            throw new common_1.HttpException({
                message: 'Map export health check failed',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async testROS2Connection() {
        try {
            this.logger.log('API: Testing ROS2 connection to *************:9090');
            const connectionTest = await this.robotsService.getRobotData({
                ip_address: '*************',
                port: 9090
            });
            let demoStream = null;
            if (connectionTest.connection_status === 'connected') {
                const streamConfig = {
                    ip_address: '*************',
                    port: 9090,
                    include_robot_data: true,
                    include_map_data: true,
                    update_frequency: 2,
                    session_id: `demo_stream_${Date.now()}`,
                };
                demoStream = await this.realtimeDataService.startStream(streamConfig);
            }
            return {
                success: true,
                connection_test: {
                    status: connectionTest.connection_status,
                    has_position: !!connectionTest.position,
                    has_battery: !!connectionTest.battery_level,
                    last_updated: connectionTest.last_updated
                },
                demo_stream: demoStream ? {
                    success: demoStream.success,
                    session_id: demoStream.success ? 'demo_stream_started' : null,
                    error: demoStream.error
                } : null,
                websocket_info: {
                    namespace: '/realtime-robot-data',
                    url: 'ws://localhost:3001/realtime-robot-data',
                    events: ['robot_data', 'map_data', 'connection'],
                    instructions: 'Connect to WebSocket to receive real-time data'
                },
                next_steps: [
                    'Connect to WebSocket at ws://localhost:3001/realtime-robot-data',
                    'Listen for robot_data events for position and battery',
                    'Listen for map_data events for real-time map updates',
                    'Use POST /robots/map/export to download map images'
                ]
            };
        }
        catch (error) {
            this.logger.error('API: ROS2 connection test failed', error);
            throw new common_1.HttpException({
                message: 'ROS2 connection test failed',
                error: error.message,
                timestamp: new Date().toISOString(),
                troubleshooting: [
                    'Verify ROS2 system is running at *************:9090',
                    'Check network connectivity',
                    'Ensure rosbridge_server is running: ros2 launch rosbridge_server rosbridge_websocket_launch.xml'
                ]
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async saveCurrentMap() {
        try {
            this.logger.log('API: Save current map button clicked - downloading from *************:9090');
            const exportConfig = {
                ip_address: '*************',
                port: 9090,
                format: 'png',
                color_scheme: 'colored',
                filename: 'robot_map',
                add_timestamp: true,
                quality: 95,
                resolution: 'high'
            };
            const result = await this.mapExportService.exportMapImage(exportConfig);
            this.logger.log(`API: Map saved successfully - ${result.filename}`);
            return {
                success: true,
                filename: result.filename,
                download_url: `/api/v1/robots/map/download/${result.filename}`,
                file_size: result.file_size,
                timestamp: new Date().toISOString(),
                message: 'Map saved successfully'
            };
        }
        catch (error) {
            this.logger.error('API: Save current map failed', error);
            throw new common_1.HttpException({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString(),
                troubleshooting: [
                    'Verify ROS2 system is running at *************:9090',
                    'Check network connectivity',
                    'Ensure map data is available from robot'
                ]
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async downloadMapFile(filename, res) {
        try {
            const sanitizedFilename = path.basename(filename);
            const filePath = path.join(process.cwd(), 'exports', 'maps', sanitizedFilename);
            this.logger.log(`API: Download request for map file: ${sanitizedFilename}`);
            if (!fs.existsSync(filePath)) {
                this.logger.warn(`API: Map file not found: ${filePath}`);
                throw new common_1.HttpException({
                    message: 'Map file not found',
                    filename: sanitizedFilename,
                    timestamp: new Date().toISOString(),
                }, common_1.HttpStatus.NOT_FOUND);
            }
            const stats = fs.statSync(filePath);
            res.setHeader('Content-Type', 'image/png');
            res.setHeader('Content-Disposition', `attachment; filename="${sanitizedFilename}"`);
            res.setHeader('Content-Length', stats.size);
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
            this.logger.log(`API: Map file downloaded successfully: ${sanitizedFilename} (${stats.size} bytes)`);
        }
        catch (error) {
            this.logger.error(`API: Map file download failed for ${filename}`, error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                message: 'Failed to download map file',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.RobotsController = RobotsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Register a new robot (Admin only)" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_robot_dto_1.CreateRobotDto]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get all robots" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get robot by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Update robot (Admin only)" }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_robot_dto_1.UpdateRobotDto]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(":id/status"),
    (0, swagger_1.ApiOperation)({ summary: "Update robot status" }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete robot (Admin only)' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('data/retrieve'),
    (0, swagger_1.ApiOperation)({
        summary: 'Retrieve real-time robot data from ROS2 bridge',
        description: 'Connects to a specific ROS2 bridge IP and retrieves robot position and battery level using Foxglove SDK',
    }),
    (0, swagger_1.ApiBody)({
        type: get_robot_data_dto_1.GetRobotDataDto,
        description: 'ROS2 bridge connection details',
        examples: {
            example1: {
                summary: 'Basic request',
                value: {
                    ip_address: '*************',
                    port: 9090,
                },
            },
            example2: {
                summary: 'Request with default port',
                value: {
                    ip_address: '*************',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Successfully retrieved robot data',
        type: get_robot_data_dto_1.RobotDataResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data (e.g., invalid IP address)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error during data retrieval',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_robot_data_dto_1.GetRobotDataDto]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "retrieveRobotData", null);
__decorate([
    (0, common_1.Post)('map/retrieve'),
    (0, swagger_1.ApiOperation)({
        summary: 'Retrieve static map data from ROS2 bridge',
        description: 'Connects to a specific ROS2 bridge IP and retrieves the current static map data from /map topic',
    }),
    (0, swagger_1.ApiBody)({
        type: get_map_data_dto_1.GetMapDataDto,
        description: 'ROS2 bridge connection details for map retrieval',
        examples: {
            example1: {
                summary: 'Basic map request',
                value: {
                    ip_address: '*************',
                    port: 9090,
                },
            },
            example2: {
                summary: 'Request with default port',
                value: {
                    ip_address: '*************',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Successfully retrieved map data',
        type: get_map_data_dto_1.MapDataResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data (e.g., invalid IP address)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error during map data retrieval',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_map_data_dto_1.GetMapDataDto]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "retrieveMapData", null);
__decorate([
    (0, common_1.Get)('data/health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check for robot data service',
        description: 'Returns the health status of the robot data retrieval service',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service health status',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', example: 'healthy' },
                python_script_exists: { type: 'boolean', example: true },
                timestamp: { type: 'string', example: '2025-07-28T14:30:00.000Z' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "robotDataHealthCheck", null);
__decorate([
    (0, common_1.Get)('realtime/streams'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get active real-time streams',
        description: 'Returns a list of all currently active real-time data streams',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of active streams',
        type: [realtime_data_dto_1.RealtimeStreamStatusDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "getActiveStreams", null);
__decorate([
    (0, common_1.Post)('realtime/start'),
    (0, swagger_1.ApiOperation)({
        summary: 'Start real-time data stream (REST endpoint)',
        description: 'Start a real-time data stream for robot and/or map data. Note: Use WebSocket connection for receiving the actual data.',
    }),
    (0, swagger_1.ApiBody)({
        type: realtime_data_dto_1.StartRealtimeStreamDto,
        description: 'Stream configuration',
        examples: {
            robotAndMap: {
                summary: 'Robot and map data stream',
                value: {
                    ip_address: '*************',
                    port: 9090,
                    include_robot_data: true,
                    include_map_data: true,
                    update_frequency: 2,
                },
            },
            robotOnly: {
                summary: 'Robot data only',
                value: {
                    ip_address: '*************',
                    include_robot_data: true,
                    include_map_data: false,
                    update_frequency: 5,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Stream started successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                session_id: { type: 'string', example: 'stream_*************_9090_1722336615000' },
                message: { type: 'string', example: 'Real-time stream started successfully' },
                websocket_info: {
                    type: 'object',
                    properties: {
                        namespace: { type: 'string', example: '/realtime-robot-data' },
                        events: { type: 'array', items: { type: 'string' }, example: ['robot_data', 'map_data', 'connection'] },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [realtime_data_dto_1.StartRealtimeStreamDto]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "startRealtimeStream", null);
__decorate([
    (0, common_1.Post)('realtime/stop'),
    (0, swagger_1.ApiOperation)({
        summary: 'Stop real-time data stream',
        description: 'Stop an active real-time data stream by session ID',
    }),
    (0, swagger_1.ApiBody)({
        type: realtime_data_dto_1.StopRealtimeStreamDto,
        description: 'Stream session to stop',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Stream stopped successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                session_id: { type: 'string', example: 'stream_*************_9090_1722336615000' },
                message: { type: 'string', example: 'Real-time stream stopped successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [realtime_data_dto_1.StopRealtimeStreamDto]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "stopRealtimeStream", null);
__decorate([
    (0, common_1.Post)('map/export'),
    (0, swagger_1.ApiOperation)({
        summary: 'Export robot map to image file',
        description: 'Export the current robot map as PNG or JPEG image with customizable options',
    }),
    (0, swagger_1.ApiBody)({
        type: map_export_dto_1.ExportMapImageDto,
        description: 'Map export configuration',
        examples: {
            basic: {
                summary: 'Basic PNG export',
                value: {
                    ip_address: '*************',
                    port: 9090,
                    format: 'png',
                    color_scheme: 'colored',
                },
            },
            advanced: {
                summary: 'Advanced JPEG export with customization',
                value: {
                    ip_address: '*************',
                    port: 9090,
                    format: 'jpeg',
                    color_scheme: 'high_contrast',
                    scale_factor: 2.0,
                    jpeg_quality: 95,
                    include_grid: true,
                    include_robot_position: true,
                    filename: 'robot_map_custom',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Map exported successfully',
        type: map_export_dto_1.ExportMapImageResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [map_export_dto_1.ExportMapImageDto]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "exportMapImage", null);
__decorate([
    (0, common_1.Post)('map/export/batch'),
    (0, swagger_1.ApiOperation)({
        summary: 'Batch export maps from multiple robots',
        description: 'Export maps from multiple robots simultaneously with optional archive creation',
    }),
    (0, swagger_1.ApiBody)({
        type: map_export_dto_1.BatchExportMapDto,
        description: 'Batch export configuration',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Batch export completed',
        type: map_export_dto_1.BatchExportResponseDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [map_export_dto_1.BatchExportMapDto]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "batchExportMaps", null);
__decorate([
    (0, common_1.Get)('map/export/history'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get map export history',
        description: 'Retrieve a list of previously exported map files',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Export history retrieved',
        type: [Object],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "getMapExportHistory", null);
__decorate([
    (0, common_1.Get)('map/export/health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check for map export service',
        description: 'Check the health status of the map export service',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Export service health status',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', example: 'healthy' },
                python_script_exists: { type: 'boolean', example: true },
                export_directory_exists: { type: 'boolean', example: true },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "mapExportHealthCheck", null);
__decorate([
    (0, common_1.Post)('test/connection'),
    (0, swagger_1.ApiOperation)({
        summary: 'Test ROS2 connection and start demo stream',
        description: 'Quick test endpoint to verify ROS2 connection to *************:9090 and start a demo real-time stream',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Connection test completed',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                connection_test: { type: 'object' },
                demo_stream: { type: 'object' },
                websocket_info: { type: 'object' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "testROS2Connection", null);
__decorate([
    (0, common_1.Post)('map/save'),
    (0, swagger_1.ApiOperation)({
        summary: 'Save current map (Frontend-friendly endpoint)',
        description: 'Simple endpoint for frontend "Save Map" button - downloads current map as PNG from *************:9090',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Map saved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                filename: { type: 'string', example: 'robot_map_2025-07-31_14-30-00.png' },
                download_url: { type: 'string', example: '/api/v1/robots/map/download/robot_map_2025-07-31_14-30-00.png' },
                file_size: { type: 'number', example: 245760 },
                timestamp: { type: 'string', example: '2025-07-31T14:30:00.000Z' },
                message: { type: 'string', example: 'Map saved successfully' }
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Map save failed',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                error: { type: 'string', example: 'Failed to connect to ROS2 system' },
                timestamp: { type: 'string', example: '2025-07-31T14:30:00.000Z' }
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "saveCurrentMap", null);
__decorate([
    (0, common_1.Get)('map/download/:filename'),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Download exported map file',
        description: 'Downloads a previously exported map file by filename',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Map file downloaded successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Unauthorized - Invalid or missing authentication token',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Map file not found',
    }),
    __param(0, (0, common_1.Param)('filename')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "downloadMapFile", null);
exports.RobotsController = RobotsController = RobotsController_1 = __decorate([
    (0, swagger_1.ApiTags)("Robots"),
    (0, common_1.Controller)("robots"),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [robots_service_1.RobotsService,
        realtime_data_service_1.RealtimeDataService,
        map_export_service_1.MapExportService])
], RobotsController);
//# sourceMappingURL=robots.controller.js.map