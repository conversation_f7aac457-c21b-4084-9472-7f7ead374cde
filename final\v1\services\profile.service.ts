/**
 * Profile Service - Handles user profile related API calls
 */

export interface UserProfile {
  userId: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  language: string;
  jobTitle: {
    id: string;
    name: string;
    description: string;
  };
  department: {
    id: string;
    name: string;
    description: string;
  };
  picture: string;
  lastLogin: string | null;
}

export interface ProfileApiResponse {
  success: boolean;
  data?: UserProfile;
  error?: string;
  message?: string;
}

class ProfileService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = 'http://localhost:3001/api/v1';
  }

  private getAuthTokens() {
    return {
      customToken: localStorage.getItem('customToken'),
      authToken: localStorage.getItem('authToken'),
    };
  }

  private getAuthHeaders(): Record<string, string> {
    const { customToken, authToken } = this.getAuthTokens();
    const token = customToken || authToken;
    
    if (!token) {
      throw new Error('No authentication token found');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  }

  /**
   * Fetch user profile data
   */
  async fetchProfile(): Promise<UserProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/profile`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch profile: ${response.status}`);
      }

      const profileData = await response.json();
      return profileData;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error instanceof Error ? error : new Error('Failed to load profile');
    }
  }

  /**
   * Update user profile data
   */
  async updateProfile(profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/profile`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update profile: ${response.status}`);
      }

      const updatedProfile = await response.json();
      return updatedProfile;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error instanceof Error ? error : new Error('Failed to update profile');
    }
  }
}

// Export singleton instance
export const profileService = new ProfileService();
