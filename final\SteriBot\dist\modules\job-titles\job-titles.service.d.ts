import { FirebaseService } from "../../config/firebase/firebase.service";
import { DepartmentsService } from "../departments/departments.service";
import type { CreateJobTitleDto } from "./dto/create-job-title.dto";
import type { UpdateJobTitleDto } from "./dto/update-job-title.dto";
export declare class JobTitlesService {
    private firebaseService;
    private departmentsService;
    private readonly collection;
    constructor(firebaseService: FirebaseService, departmentsService: DepartmentsService);
    create(createJobTitleDto: CreateJobTitleDto): Promise<{
        createdAt: Date;
        updatedAt: Date;
        name: string;
        description?: string;
        departmentId: string;
        level?: string;
        id: string;
    }>;
    findAll(): Promise<any[]>;
    findById(id: string): Promise<any>;
    findByNameAndDepartment(name: string, departmentId: string): Promise<{
        id: string;
    }>;
    findByDepartment(departmentId: string): Promise<{
        id: string;
    }[]>;
    update(id: string, updateJobTitleDto: UpdateJobTitleDto): Promise<any>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
