import { <PERSON>du<PERSON> } from "@nestjs/common"
import { RobotsController } from "./robots.controller"
import { RobotsService } from "./robots.service"
import { RealtimeDataService } from "./services/realtime-data.service"
import { RealtimeDataGateway } from "./gateways/realtime-data.gateway"
import { MapExportService } from "./services/map-export.service"

@Module({
  controllers: [RobotsController],
  providers: [RobotsService, RealtimeDataService, RealtimeDataGateway, MapExportService],
  exports: [RobotsService, RealtimeDataService, MapExportService],
})
export class RobotsModule {}
