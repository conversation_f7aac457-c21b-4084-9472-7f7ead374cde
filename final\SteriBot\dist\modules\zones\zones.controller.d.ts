import { ZonesService } from "./zones.service";
import { CreateZoneDto } from "./dto/create-zone.dto";
import { UpdateZoneDto } from "./dto/update-zone.dto";
export declare class ZonesController {
    private readonly zonesService;
    constructor(zonesService: ZonesService);
    create(createZoneDto: CreateZoneDto): Promise<{
        zoneId: string;
        createdAt: Date;
        zoneName: string;
        roomId: string;
        coordinates: string;
        zoneType: string;
        priority: string;
        disinfectionRequired?: boolean;
        accessPoints?: string;
        lastDisinfected?: string;
        disinfectionFrequency?: number;
    }>;
    findAll(roomId?: string): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateZoneDto: UpdateZoneDto): Promise<{
        id: string;
    }>;
    markAsDisinfected(id: string): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
