// components/RobotStatusCard.tsx
import { <PERSON><PERSON><PERSON>, G<PERSON>ge, Wifi, WifiOff } from "lucide-react"
import { useRobotData } from "@/hooks/use-robot-data"

export function RobotStatusCard() {
  const { robotData, connectionStatus } = useRobotData()

  // Use real-time data or fallback to defaults
  const status = {
    position: robotData?.position || { x: 0, y: 0 },
    battery: robotData?.battery?.percentage,
    speed: robotData?.speed,
  }

  return (
    <div
      className="p-4 rounded-lg text-white"
      style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
    >
      <h2 className="flex items-center gap-2 text-white font-bold mb-4">
        <Gauge className="w-5 h-5" />
        Robot Status
        {connectionStatus.connected ? (
          <Wifi className="w-4 h-4 text-green-300" />
        ) : (
          <WifiOff className="w-4 h-4 text-red-300" />
        )}
      </h2>
      <div className="space-y-3 text-white">
        <div className="flex justify-between">
          <span className="text-gray-300">Position</span>
          <span>X: {status.position.x.toFixed(1)}, Y: {status.position.y.toFixed(1)}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-300">Battery</span>
          <div className="flex items-center gap-2">
            <div className="w-24 h-2 bg-[#0A3F4C] rounded-full overflow-hidden border border-[#0C6980]">
              <div
                className="h-full rounded-full"
                style={{
                  width: `${status.battery || 0}%`,
                  background: 'linear-gradient(90deg, #14b8a6, #0C6980)'
                }}
              ></div>
            </div>
            <span>{status.battery !== undefined ? `${Math.round(status.battery)}%` : 'N/A'}</span>
          </div>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-300">Speed</span>
          <span>{status.speed !== undefined ? `${status.speed.toFixed(2)} m/s` : 'N/A'}</span>
        </div>
      </div>
    </div>
  )
}