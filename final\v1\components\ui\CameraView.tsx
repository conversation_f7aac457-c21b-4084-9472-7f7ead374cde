// components/ui/CameraView.tsx
import { useState } from 'react';
import { Maximize2, Minimize2 } from 'lucide-react';

interface CameraViewProps {
  isFullScreen?: boolean;
  onToggleFullScreen?: () => void;
}

export function CameraView({ isFullScreen = false, onToggleFullScreen }: CameraViewProps) {
  const [isConnected, setIsConnected] = useState(true);

  return (
    <div className={`bg-gray-700 rounded-lg overflow-hidden flex flex-col ${isFullScreen ? 'fixed inset-0 z-50' : 'flex-1'}`}>
      {/* Camera Header */}
      <div className="bg-gray-800 p-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm text-white">Camera Feed</span>
        </div>
        <button
          onClick={onToggleFullScreen}
          className="text-white hover:text-gray-300 p-1 rounded transition-colors"
          title={isFullScreen ? "Exit Full Screen" : "Maximize Screen"}
        >
          {isFullScreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
        </button>
      </div>

      {/* Camera Content */}
      <div className="flex-1 relative bg-black">
        {isConnected ? (
          <div className="w-full h-full flex items-center justify-center">
            {/* Placeholder for actual camera feed */}
            <div className="text-center text-gray-400">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-600 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"/>
                  <circle cx="12" cy="13" r="3"/>
                </svg>
              </div>
              <p className="text-sm">Live Camera Feed</p>
              <p className="text-xs text-gray-500 mt-1">Robot Camera View</p>
            </div>
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="w-16 h-16 mx-auto mb-4 bg-red-600 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"/>
                  <circle cx="12" cy="13" r="3"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </div>
              <p className="text-sm">Camera Disconnected</p>
              <p className="text-xs text-gray-500 mt-1">Check robot connection</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
