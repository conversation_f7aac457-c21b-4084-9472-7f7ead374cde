import { UsersService } from "./users.service";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { UpdatePhotoResponseDto } from "./dto/update-photo.dto";
import { UserRole } from "../../common/decorators/roles.decorator";
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<{
        createdAt: Date;
        lastLogin: Date;
        userId: string;
        username: string;
        firstName: string;
        lastName: string;
        email: string;
        role: UserRole;
        language?: string;
        jobTitleId?: string;
        departmentId?: string;
        picture?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<any>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<any>;
    remove(id: string): Promise<{
        message: string;
    }>;
    updatePhoto(userId: string, file: any, req: any): Promise<UpdatePhotoResponseDto>;
}
