"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
const file_validation_service_1 = require("../../common/validators/file-validation.service");
let UsersService = class UsersService {
    constructor(firebaseService, fileValidationService) {
        this.firebaseService = firebaseService;
        this.fileValidationService = fileValidationService;
        this.collection = "users";
    }
    async create(createUserDto) {
        const firestore = this.firebaseService.getFirestore();
        const userData = {
            ...createUserDto,
            createdAt: createUserDto.createdAt ? new Date(createUserDto.createdAt) : new Date(),
            lastLogin: createUserDto.lastLogin ? new Date(createUserDto.lastLogin) : null,
        };
        const docRef = firestore.collection(this.collection).doc(createUserDto.userId);
        await docRef.set(userData);
        return userData;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        const userData = { id: doc.id, ...doc.data() };
        return this.populateUserData(userData);
    }
    async findByEmail(email) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).where('email', '==', email).get();
        if (snapshot.empty) {
            return null;
        }
        const doc = snapshot.docs[0];
        return { id: doc.id, ...doc.data() };
    }
    async update(id, updateUserDto) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const docRef = firestore.collection(this.collection).doc(id);
            const doc = await docRef.get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            const updateData = {
                ...updateUserDto,
                updatedAt: new Date(),
            };
            if (updateUserDto.createdAt) {
                updateData.createdAt = new Date(updateUserDto.createdAt);
            }
            if (updateUserDto.lastLogin) {
                updateData.lastLogin = new Date(updateUserDto.lastLogin);
            }
            await docRef.update(updateData);
            return this.findById(id);
        }
        catch (error) {
            console.error('Error updating user:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new Error(`Failed to update user: ${error.message}`);
        }
    }
    async updateLastLogin(userId) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(userId);
        await docRef.update({
            lastLogin: new Date(),
        });
    }
    async remove(id) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const doc = await firestore.collection(this.collection).doc(id).get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            const userData = doc.data();
            const firebaseAuthId = userData?.userId || id;
            await firestore.collection(this.collection).doc(id).delete();
            try {
                await this.firebaseService.getAuth().deleteUser(firebaseAuthId);
            }
            catch (authError) {
                console.warn(`Could not delete user from Firebase Auth: ${authError.message}`);
            }
            return { message: "User deleted successfully" };
        }
        catch (error) {
            console.error('Error deleting user:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new Error(`Failed to delete user: ${error.message}`);
        }
    }
    async populateUserData(userData) {
        return userData;
    }
    async createProfileResponse(userData, jobTitlesService, departmentsService) {
        const populatedUser = { ...userData };
        if (userData.jobTitleId && jobTitlesService) {
            try {
                const jobTitle = await jobTitlesService.findById(userData.jobTitleId);
                populatedUser.jobTitle = {
                    id: jobTitle.id,
                    name: jobTitle.name,
                    description: jobTitle.description
                };
            }
            catch (error) {
                console.warn(`Job title ${userData.jobTitleId} not found for user ${userData.id}`);
            }
        }
        if (userData.departmentId && departmentsService) {
            try {
                const department = await departmentsService.findById(userData.departmentId);
                populatedUser.department = {
                    id: department.id,
                    name: department.name,
                    description: department.description
                };
            }
            catch (error) {
                console.warn(`Department ${userData.departmentId} not found for user ${userData.id}`);
            }
        }
        return {
            userId: populatedUser.id || populatedUser.userId,
            username: populatedUser.username,
            firstName: populatedUser.firstName,
            lastName: populatedUser.lastName,
            email: populatedUser.email,
            role: populatedUser.role,
            language: populatedUser.language || 'en',
            jobTitle: populatedUser.jobTitle || null,
            department: populatedUser.department || null,
            picture: populatedUser.picture || null,
            lastLogin: populatedUser.lastLogin ?
                (populatedUser.lastLogin instanceof Date ?
                    populatedUser.lastLogin.toISOString() :
                    populatedUser.lastLogin) : null
        };
    }
    async updatePhoto(userId, file) {
        try {
            this.fileValidationService.validateImageFile(file);
            const user = await this.findById(userId);
            if (!user) {
                throw new common_1.NotFoundException(`User with ID ${userId} not found`);
            }
            if (user.picture && user.picture.includes('storage.googleapis.com')) {
                try {
                    const oldFilePath = this.firebaseService.extractFilePathFromUrl(user.picture);
                    await this.firebaseService.deleteFile(oldFilePath);
                }
                catch (error) {
                    console.warn('Could not delete old profile photo:', error.message);
                }
            }
            const fileName = this.fileValidationService.generateUniqueFileName(userId, file.originalname);
            const contentType = this.fileValidationService.getContentTypeFromExtension(fileName);
            const photoUrl = await this.firebaseService.uploadFile(file.buffer, fileName, contentType, 'profile-pictures');
            const firestore = this.firebaseService.getFirestore();
            await firestore.collection(this.collection).doc(userId).update({
                picture: photoUrl,
                updatedAt: new Date()
            });
            return {
                message: 'Profile photo updated successfully',
                photoUrl,
                userId
            };
        }
        catch (error) {
            console.error('Error updating user photo:', error);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to update profile photo');
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService,
        file_validation_service_1.FileValidationService])
], UsersService);
//# sourceMappingURL=users.service.js.map