import { Module } from "@nestjs/common"
import { DepartmentsService } from "./departments.service"
import { DepartmentsController } from "./departments.controller"
import { FirebaseModule } from "../../config/firebase/firebase.module"

@Module({
  imports: [FirebaseModule],
  controllers: [DepartmentsController],
  providers: [DepartmentsService],
  exports: [DepartmentsService],
})
export class DepartmentsModule {}
