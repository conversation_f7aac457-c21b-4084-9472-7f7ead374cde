"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RealtimeDataService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealtimeDataService = void 0;
const common_1 = require("@nestjs/common");
const child_process_1 = require("child_process");
const path = require("path");
let RealtimeDataService = RealtimeDataService_1 = class RealtimeDataService {
    constructor() {
        this.logger = new common_1.Logger(RealtimeDataService_1.name);
        this.activeStreams = new Map();
        this.dataEventHandlers = [];
        this.pythonScriptPath = path.join(process.cwd(), 'Ros_api', 'realtime_robot_bridge.py');
    }
    onDataReceived(handler) {
        this.dataEventHandlers.push(handler);
    }
    emitEvent(eventType, data) {
        this.dataEventHandlers.forEach(handler => {
            try {
                handler(eventType, data);
            }
            catch (error) {
                this.logger.error(`Error in event handler: ${error.message}`);
            }
        });
    }
    async startStream(config) {
        try {
            if (this.activeStreams.has(config.session_id)) {
                return { success: false, error: 'Stream already exists' };
            }
            this.logger.log(`🚀 Starting realtime stream: ${config.session_id}`);
            const stream = {
                config,
                process: null,
                status: 'connecting',
                started_at: new Date().toISOString(),
                messages_received: 0,
            };
            this.activeStreams.set(config.session_id, stream);
            const pythonArgs = [
                this.pythonScriptPath,
                '--ip', config.ip_address,
                '--port', config.port.toString(),
                '--session-id', config.session_id,
                '--update-frequency', config.update_frequency.toString(),
            ];
            if (config.include_robot_data) {
                pythonArgs.push('--include-robot-data');
            }
            if (config.include_map_data) {
                pythonArgs.push('--include-map-data');
            }
            this.logger.log(`🐍 Starting Python process: python ${pythonArgs.join(' ')}`);
            const pythonProcess = (0, child_process_1.spawn)('python', pythonArgs, {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: process.cwd(),
            });
            stream.process = pythonProcess;
            pythonProcess.stdout.on('data', (data) => {
                this.handlePythonOutput(config.session_id, data.toString());
            });
            pythonProcess.stderr.on('data', (data) => {
                this.logger.error(`Python stderr [${config.session_id}]: ${data.toString()}`);
            });
            pythonProcess.on('close', (code) => {
                this.logger.log(`Python process closed [${config.session_id}]: ${code}`);
                this.handleStreamDisconnection(config.session_id, `Process exited with code ${code}`);
            });
            pythonProcess.on('error', (error) => {
                this.logger.error(`Python process error [${config.session_id}]: ${error.message}`);
                this.handleStreamError(config.session_id, error.message);
            });
            this.emitConnectionEvent(config.session_id, 'connecting');
            return { success: true };
        }
        catch (error) {
            this.logger.error(`Failed to start stream: ${error.message}`);
            this.activeStreams.delete(config.session_id);
            return { success: false, error: error.message };
        }
    }
    async stopStream(sessionId) {
        try {
            const stream = this.activeStreams.get(sessionId);
            if (!stream) {
                return { success: false, error: 'Stream not found' };
            }
            this.logger.log(`🛑 Stopping stream: ${sessionId}`);
            if (stream.process && !stream.process.killed) {
                stream.process.kill('SIGTERM');
                setTimeout(() => {
                    if (stream.process && !stream.process.killed) {
                        stream.process.kill('SIGKILL');
                    }
                }, 5000);
            }
            this.activeStreams.delete(sessionId);
            this.emitConnectionEvent(sessionId, 'disconnected');
            return { success: true };
        }
        catch (error) {
            this.logger.error(`Failed to stop stream: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    getActiveStreams() {
        const streams = [];
        for (const [sessionId, stream] of this.activeStreams) {
            streams.push({
                session_id: sessionId,
                status: stream.status,
                ip_address: stream.config.ip_address,
                port: stream.config.port,
                include_robot_data: stream.config.include_robot_data,
                include_map_data: stream.config.include_map_data,
                update_frequency: stream.config.update_frequency,
                started_at: stream.started_at,
                last_data_received: stream.last_data_received,
                messages_received: stream.messages_received,
                connected_clients: this.getConnectedClientsCount(),
            });
        }
        return streams;
    }
    handlePythonOutput(sessionId, output) {
        try {
            const lines = output.trim().split('\n');
            for (const line of lines) {
                if (!line.trim())
                    continue;
                try {
                    const data = JSON.parse(line);
                    this.processPythonData(sessionId, data);
                }
                catch (parseError) {
                    this.logger.debug(`Python log [${sessionId}]: ${line}`);
                }
            }
        }
        catch (error) {
            this.logger.error(`Error handling Python output: ${error.message}`);
        }
    }
    processPythonData(sessionId, data) {
        const stream = this.activeStreams.get(sessionId);
        if (!stream)
            return;
        stream.messages_received++;
        stream.last_data_received = new Date().toISOString();
        switch (data.type) {
            case 'connection':
                this.handleConnectionData(sessionId, data);
                break;
            case 'robot_data':
                this.handleRobotData(sessionId, data);
                break;
            case 'map_data':
                this.handleMapData(sessionId, data);
                break;
            case 'error':
                this.handleStreamError(sessionId, data.message);
                break;
            default:
                this.logger.debug(`Unknown data type from Python: ${data.type}`);
        }
    }
    handleConnectionData(sessionId, data) {
        const stream = this.activeStreams.get(sessionId);
        if (!stream)
            return;
        if (data.status === 'connected') {
            stream.status = 'connected';
            this.emitConnectionEvent(sessionId, 'connected');
        }
        else if (data.status === 'disconnected') {
            this.handleStreamDisconnection(sessionId, data.message);
        }
    }
    handleRobotData(sessionId, data) {
        const robotDataEvent = {
            event: 'robot_data',
            session_id: sessionId,
            position: data.position,
            battery_level: data.battery_level,
            timestamp: data.timestamp || new Date().toISOString(),
            sequence: data.sequence || 0,
        };
        this.emitEvent('robot_data', robotDataEvent);
    }
    handleMapData(sessionId, data) {
        const mapDataEvent = {
            event: 'map_data',
            session_id: sessionId,
            info: data.info,
            data: data.data,
            statistics: data.statistics,
            is_full_update: data.is_full_update || true,
            timestamp: data.timestamp || new Date().toISOString(),
            sequence: data.sequence || 0,
            data_length: data.data ? data.data.length : 0,
        };
        this.emitEvent('map_data', mapDataEvent);
    }
    handleStreamError(sessionId, errorMessage) {
        const stream = this.activeStreams.get(sessionId);
        if (stream) {
            stream.status = 'error';
            stream.error_message = errorMessage;
        }
        this.emitConnectionEvent(sessionId, 'error', errorMessage);
    }
    handleStreamDisconnection(sessionId, reason) {
        const stream = this.activeStreams.get(sessionId);
        if (stream) {
            stream.status = 'disconnected';
        }
        this.emitConnectionEvent(sessionId, 'disconnected', reason);
    }
    emitConnectionEvent(sessionId, event, errorMessage) {
        const stream = this.activeStreams.get(sessionId);
        if (!stream)
            return;
        const connectionEvent = {
            event,
            session_id: sessionId,
            ip_address: stream.config.ip_address,
            port: stream.config.port,
            timestamp: new Date().toISOString(),
            error_message: errorMessage,
        };
        this.emitEvent('connection', connectionEvent);
    }
    getConnectedClientsCount() {
        return 0;
    }
    async cleanup() {
        this.logger.log('🧹 Cleaning up realtime data service...');
        const stopPromises = Array.from(this.activeStreams.keys()).map(sessionId => this.stopStream(sessionId));
        await Promise.all(stopPromises);
        this.logger.log('✅ Cleanup completed');
    }
};
exports.RealtimeDataService = RealtimeDataService;
exports.RealtimeDataService = RealtimeDataService = RealtimeDataService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], RealtimeDataService);
//# sourceMappingURL=realtime-data.service.js.map