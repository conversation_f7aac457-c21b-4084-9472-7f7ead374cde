/**
 * Profile Hook
 */

import { useState, useCallback } from 'react';
import { profileAPI, UserProfile, ProfileUpdateData } from '@/api';

export interface UseProfileReturn {
  profile: UserProfile | null;
  isLoading: boolean;
  isUpdating: boolean;
  error: string | null;
  fetchProfile: () => Promise<UserProfile | null>;
  updateProfile: (data: ProfileUpdateData) => Promise<boolean>;
  clearError: () => void;
}

export const useProfile = (): UseProfileReturn => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Fetch profile
  const fetchProfile = useCallback(async (): Promise<UserProfile | null> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await profileAPI.fetchProfile();
      
      if (response.success && response.data) {
        setProfile(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to fetch profile');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch profile';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update profile
  const updateProfile = useCallback(async (data: ProfileUpdateData): Promise<boolean> => {
    try {
      setIsUpdating(true);
      setError(null);
      
      const response = await profileAPI.updateProfile(data);
      
      if (response.success && response.data) {
        setProfile(response.data);
        return true;
      } else {
        setError(response.error || 'Failed to update profile');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile';
      setError(errorMessage);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, []);

  return {
    profile,
    isLoading,
    isUpdating,
    error,
    fetchProfile,
    updateProfile,
    clearError,
  };
};
