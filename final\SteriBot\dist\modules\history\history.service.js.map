{"version": 3, "file": "history.service.js", "sourceRoot": "", "sources": ["../../../src/modules/history/history.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA8D;AAC9D,6EAAwE;AAkBjE,IAAM,cAAc,GAApB,MAAM,cAAc;IAGzB,YAAoB,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;QAFnC,eAAU,GAAG,SAAS,CAAA;IAEgB,CAAC;IAExD,KAAK,CAAC,MAAM,CAAC,gBAAkC;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,CAAA;QAE1D,MAAM,WAAW,GAAG;YAClB,GAAG,gBAAgB;YACnB,SAAS,EAAE,MAAM,CAAC,EAAE;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAA;QAED,MAAM,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;QAC7B,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAA;QAC/F,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IACpE,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,QAAQ,GAAG,MAAM,SAAS;aAC7B,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;aAC3B,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;aAC7B,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B,GAAG,EAAE,CAAA;QAER,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IACpE,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,QAAQ,GAAG,MAAM,SAAS;aAC7B,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;aAC3B,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;aAC7B,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B,GAAG,EAAE,CAAA;QAER,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IACpE,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,QAAQ,GAAG,MAAM,SAAS;aAC7B,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;aAC3B,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC;aAC/B,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B,GAAG,EAAE,CAAA;QAER,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IACpE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,GAAG,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAA;QAErE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAA;QACvE,CAAC;QAED,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAA;IACtC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAY;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,KAAK,GAA4D,aAAa,CAAC;QAEnF,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACzC,KAAK,GAAG,KAAK;iBACV,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;iBACrD,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;QACxD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;QACrD,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;QACvD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;QACrD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAA;QAC/D,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IACpE,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAY;QAC3B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;QAG/C,MAAM,OAAO,GAAG;YACd,IAAI;YACJ,SAAS;YACT,SAAS;YACT,UAAU;YACV,SAAS;YACT,QAAQ;YACR,WAAW;YACX,UAAU;YACV,QAAQ;YACR,eAAe;YACf,cAAc;YACd,yBAAyB;SAC1B,CAAA;QAED,MAAM,OAAO,GAAoB,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;QAGnE,OAAO;YACL,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,kBAAkB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;YACxE,WAAW,EAAE,UAAU;SACxB,CAAA;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAA;QAC5D,OAAO,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAA;IAC3D,CAAC;CACF,CAAA;AAlIY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAI0B,kCAAe;GAHzC,cAAc,CAkI1B"}