# Real-time Robot Data Streaming Integration Guide

This guide provides comprehensive instructions for integrating with the SteriBot real-time data streaming system using WebSocket connections.

## File Structure

```
SteriApp/
├── docs/                           # Documentation files
│   ├── REALTIME_INTEGRATION_GUIDE.md
│   └── ...
├── examples/                       # Integration examples
│   ├── react-realtime-robot-dashboard.tsx
│   └── ...
├── Ros_api/                        # Python ROS2 bridge scripts
│   ├── realtime_robot_bridge.py    # Real-time streaming bridge
│   ├── map_exporter.py             # Map export utility
│   ├── ros2_data_retriever.py      # Static data retrieval
│   └── ros2_map_retriever.py       # Static map retrieval
└── src/modules/robots/             # NestJS backend services
    ├── services/
    │   ├── realtime-data.service.ts
    │   └── map-export.service.ts
    └── gateways/
        └── realtime-data.gateway.ts
```

## Overview

The SteriBot system now supports real-time streaming of:
- **Robot Data**: Position, battery level, status information
- **Map Data**: Occupancy grid maps with real-time updates
- **Map Export**: PNG/JPEG export functionality with customizable options

## Architecture

```
Frontend (React/Vue/Angular) 
    ↓ WebSocket Connection
Backend (NestJS + Socket.IO)
    ↓ Python Bridge Process
ROS2 Robot System (Foxglove SDK)
```

## WebSocket Connection Setup

### 1. Install Socket.IO Client

```bash
npm install socket.io-client
```

### 2. Basic Connection

```typescript
import { io, Socket } from 'socket.io-client';

// Connect to the real-time data namespace
const socket: Socket = io('http://localhost:3000/realtime-robot-data', {
  transports: ['websocket'],
  autoConnect: true,
});

// Connection event handlers
socket.on('connect', () => {
  console.log('✅ Connected to real-time data stream');
  console.log('Socket ID:', socket.id);
});

socket.on('disconnect', () => {
  console.log('❌ Disconnected from real-time data stream');
});

socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});
```

## Starting Real-time Streams

### 1. Start Stream via REST API

```typescript
// Start a real-time stream
const startStream = async (robotIP: string) => {
  try {
    const response = await fetch('/api/robots/realtime/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ip_address: robotIP,
        port: 8765,
        include_robot_data: true,
        include_map_data: true,
        update_frequency: 2, // Hz
      }),
    });

    const result = await response.json();
    if (result.success) {
      console.log('Stream started:', result.session_id);
      return result.session_id;
    }
  } catch (error) {
    console.error('Failed to start stream:', error);
  }
};
```

### 2. Listen for Real-time Events

```typescript
// Robot data events
socket.on('robot_data', (data: RealtimeRobotDataEvent) => {
  console.log('Robot data received:', data);
  
  // Update UI with robot position
  if (data.position) {
    updateRobotPosition(data.position.x, data.position.y, data.position.theta);
  }
  
  // Update battery indicator
  if (data.battery_level) {
    updateBatteryLevel(data.battery_level.percentage);
  }
});

// Map data events
socket.on('map_data', (data: RealtimeMapDataEvent) => {
  console.log('Map data received:', data);
  
  // Update map visualization
  if (data.info && data.data) {
    updateMapVisualization(data.info, data.data);
  }
});

// Stream status events
socket.on('stream_status', (status) => {
  console.log('Stream status:', status);
  updateStreamStatus(status);
});
```

## Data Structures

### Robot Data Event

```typescript
interface RealtimeRobotDataEvent {
  event_type: 'robot_data';
  robot_id: string;
  session_id: string;
  timestamp: string;
  
  position?: {
    x: number;
    y: number;
    z: number;
    theta: number;
    frame_id: string;
    source_topic: string;
  };
  
  battery_level?: {
    percentage?: number;
    voltage?: number;
    current?: number;
    temperature?: number;
    charging?: boolean;
    source_topic: string;
  };
}
```

### Map Data Event

```typescript
interface RealtimeMapDataEvent {
  event_type: 'map_data';
  robot_id: string;
  session_id: string;
  timestamp: string;
  
  info?: {
    width: number;
    height: number;
    resolution: number;
    origin: { x: number; y: number; z: number };
  };
  
  data?: number[]; // Occupancy grid: -1=unknown, 0=free, 100=occupied
  
  statistics?: {
    total_cells: number;
    free_cells: number;
    occupied_cells: number;
    unknown_cells: number;
  };
}
```

## Map Visualization

### Canvas-based Map Rendering

```typescript
class MapRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private mapData: RealtimeMapDataEvent | null = null;

  constructor(canvasId: string) {
    this.canvas = document.getElementById(canvasId) as HTMLCanvasElement;
    this.ctx = this.canvas.getContext('2d')!;
  }

  updateMap(mapEvent: RealtimeMapDataEvent) {
    this.mapData = mapEvent;
    this.render();
  }

  private render() {
    if (!this.mapData?.info || !this.mapData?.data) return;

    const { width, height, resolution } = this.mapData.info;
    const data = this.mapData.data;

    // Set canvas size
    this.canvas.width = width;
    this.canvas.height = height;

    // Create image data
    const imageData = this.ctx.createImageData(width, height);
    const pixels = imageData.data;

    // Convert occupancy grid to RGBA
    for (let i = 0; i < data.length; i++) {
      const pixelIndex = i * 4;
      const occupancy = data[i];

      if (occupancy === -1) {
        // Unknown - Gray
        pixels[pixelIndex] = 128;     // R
        pixels[pixelIndex + 1] = 128; // G
        pixels[pixelIndex + 2] = 128; // B
      } else if (occupancy === 0) {
        // Free - White
        pixels[pixelIndex] = 255;     // R
        pixels[pixelIndex + 1] = 255; // G
        pixels[pixelIndex + 2] = 255; // B
      } else {
        // Occupied - Black
        pixels[pixelIndex] = 0;       // R
        pixels[pixelIndex + 1] = 0;   // G
        pixels[pixelIndex + 2] = 0;   // B
      }
      pixels[pixelIndex + 3] = 255;   // A (alpha)
    }

    // Draw to canvas
    this.ctx.putImageData(imageData, 0, 0);
  }
}

// Usage
const mapRenderer = new MapRenderer('map-canvas');

socket.on('map_data', (data: RealtimeMapDataEvent) => {
  mapRenderer.updateMap(data);
});
```

## Map Export Integration

### Export Current Map

```typescript
const exportMap = async (robotIP: string, options = {}) => {
  try {
    const response = await fetch('/api/robots/map/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ip_address: robotIP,
        port: 8765,
        format: 'png',
        color_scheme: 'colored',
        scale_factor: 2.0,
        include_grid: false,
        include_robot_position: true,
        ...options,
      }),
    });

    const result = await response.json();
    if (result.success) {
      // Download the exported image
      window.open(result.download_url, '_blank');
      return result;
    }
  } catch (error) {
    console.error('Map export failed:', error);
  }
};
```

### Batch Export from Multiple Robots

```typescript
const batchExportMaps = async (robotIPs: string[]) => {
  try {
    const response = await fetch('/api/robots/map/export/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ip_addresses: robotIPs,
        port: 8765,
        create_archive: true,
        export_config: {
          format: 'png',
          color_scheme: 'high_contrast',
          scale_factor: 1.5,
        },
      }),
    });

    const result = await response.json();
    console.log(`Exported ${result.exported_count} maps, ${result.failed_count} failed`);
    
    if (result.archive) {
      // Download the archive
      window.open(result.archive.download_url, '_blank');
    }
    
    return result;
  } catch (error) {
    console.error('Batch export failed:', error);
  }
};
```

## React Hook Example

```typescript
import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';

interface UseRealtimeRobotData {
  socket: Socket | null;
  robotData: RealtimeRobotDataEvent | null;
  mapData: RealtimeMapDataEvent | null;
  isConnected: boolean;
  startStream: (robotIP: string) => Promise<string | null>;
  stopStream: (sessionId: string) => Promise<boolean>;
}

export const useRealtimeRobotData = (): UseRealtimeRobotData => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [robotData, setRobotData] = useState<RealtimeRobotDataEvent | null>(null);
  const [mapData, setMapData] = useState<RealtimeMapDataEvent | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const newSocket = io('/realtime-robot-data');
    
    newSocket.on('connect', () => setIsConnected(true));
    newSocket.on('disconnect', () => setIsConnected(false));
    newSocket.on('robot_data', setRobotData);
    newSocket.on('map_data', setMapData);
    
    setSocket(newSocket);
    
    return () => {
      newSocket.close();
    };
  }, []);

  const startStream = async (robotIP: string): Promise<string | null> => {
    try {
      const response = await fetch('/api/robots/realtime/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ip_address: robotIP,
          include_robot_data: true,
          include_map_data: true,
        }),
      });
      
      const result = await response.json();
      return result.success ? result.session_id : null;
    } catch {
      return null;
    }
  };

  const stopStream = async (sessionId: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/robots/realtime/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ session_id: sessionId }),
      });
      
      const result = await response.json();
      return result.success;
    } catch {
      return false;
    }
  };

  return {
    socket,
    robotData,
    mapData,
    isConnected,
    startStream,
    stopStream,
  };
};
```

## Error Handling

```typescript
// Handle connection errors
socket.on('connect_error', (error) => {
  console.error('Connection failed:', error);
  // Implement retry logic or user notification
});

// Handle stream errors
socket.on('stream_error', (error) => {
  console.error('Stream error:', error);
  // Handle stream-specific errors
});

// Implement reconnection logic
socket.on('disconnect', (reason) => {
  if (reason === 'io server disconnect') {
    // Server initiated disconnect, reconnect manually
    socket.connect();
  }
  // Otherwise, socket.io will automatically reconnect
});
```

## Performance Considerations

1. **Update Frequency**: Adjust `update_frequency` based on your needs (1-10 Hz recommended)
2. **Data Filtering**: Only subscribe to data you need (`include_robot_data`, `include_map_data`)
3. **Canvas Optimization**: Use `requestAnimationFrame` for smooth map rendering
4. **Memory Management**: Clean up event listeners and close connections when components unmount

## Next Steps

1. Implement the WebSocket connection in your frontend application
2. Create UI components for robot data visualization
3. Add map rendering with zoom/pan controls
4. Integrate map export functionality
5. Test with multiple robots for scalability

For more examples and advanced usage, see the `/examples` directory in the project repository.

## Example Files

- **React Dashboard**: `examples/react-realtime-robot-dashboard.tsx` - Complete React component with real-time data visualization
- **Python Scripts**: `Ros_api/` - All ROS2 bridge scripts for data retrieval and streaming
- **Documentation**: `docs/` - All project documentation and guides
